(function () {
  const config = {
    frameRate: 15,
    debug: true,
  };

  let socket: WebSocket | null = null;
  let pc: RTCPeerConnection | null = null;
  let cropX: number, cropY: number, width: number, height: number;
  let screenStream: MediaStream | null = null;
  let cropRegion = {
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  };

  // State management for initialization
  let isInitialized = false;

  function log(...args: any[]) {
    if (config.debug) {
      console.log("[kazeel][screen-cropper]", ...args);
    }
  }

  function error(...args: any[]) {
    console.error("[kazeel][screen-cropper]", ...args);
  }

  /**
   * Initialize screen cropper with WebSocket and WebRTC connections
   * This method sets up the connections but doesn't start streaming
   */
  async function init(wsEndpoint: string, viewPort: Viewport) {
    if (isInitialized) {
      log("Screen cropper already initialized");
      return "ALREADY_INITIALIZED";
    }

    try {
      log("🔧 [init] Starting screen cropper initialization...");
      log("🔧 [init] WebSocket endpoint:", wsEndpoint);
      log("🔧 [init] Viewport:", viewPort);

      // Wait for browser controller to be available before proceeding
      log("🔧 [init] Step 1: Waiting for browser controller...");
      await waitForBrowserController();
      log("✅ [init] Browser controller is ready");

      // Step 1: Setup WebSocket connection
      log("🔧 [init] Step 2: Setting up WebSocket connection...");
      await connectToSocket(wsEndpoint);
      log("✅ [init] WebSocket connection established");

      // Step 2: Setup WebRTC connection with input channel
      log("🔧 [init] Step 3: Setting up WebRTC connection...");
      await connectToWebRTC();
      log("✅ [init] WebRTC connection established");

      isInitialized = true;
      log("✅ [init] Screen cropper initialization completed successfully");
      return "SUCCESS";
    } catch (err: any) {
      throw new Error(
        `[kazeel][screen-cropper] Failed to initialize: ${err.message} | Error stack: ${err.stack}`
      );
    }
  }

  /**
   * Start streaming after initialization is complete
   * This method begins the actual screen capture and streaming
   */
  async function start(viewPort: Viewport) {
    if (!isInitialized) {
      throw new Error(
        `[kazeel][screen-cropper] Screen cropper not initialized`
      );
    }

    try {
      log("Starting screen cropper streaming");
      log("Viewport:", viewPort);

      log("Getting initial bounding box from TF detector...");
      const cropBox = await window.tfCaptchaDetector.getInitialBoundingBox(
        viewPort
      );
      log("Initial crop box:", cropBox);

      cropX = cropBox.x;
      cropY = cropBox.y;
      width = cropBox.width;
      height = cropBox.height;

      log("Starting streaming with crop box...");
      await startStreaming(viewPort, cropBox);
      log("Screen cropper streaming started successfully");
    } catch (err: any) {
      throw new Error(
        `[kazeel][screen-cropper] Failed to start streaming: ${err.message}`
      );
    }
  }

  /**
   * Connect to WebSocket and wait for it to be ready
   */
  async function connectToSocket(wsEndpoint: string) {
    return new Promise<void>((resolve, reject) => {
      try {
        log("🔧 [connectToSocket] Connecting to WebSocket:", wsEndpoint);

        if (!wsEndpoint) {
          throw new Error(
            `[kazeel][screen-cropper] WebSocket endpoint is required`
          );
        }

        socket = new WebSocket(wsEndpoint);

        log("✅ [connectToSocket] Socket exposed to window");

        socket.addEventListener("open", () => {
          log("✅ [connectToSocket] WebSocket connected successfully");
          resolve();
        });

        socket.addEventListener("error", (e) => {
          reject(
            new Error(
              `[kazeel][screen-cropper] WebSocket connection failed: ${
                e.type || "Unknown error"
              }`
            )
          );
          return null;
        });

        socket.addEventListener("close", (e) => {
          log(
            "🔌 [connectToSocket] WebSocket connection closed:",
            e.code,
            e.reason
          );
        });

        // Set up message handlers
        setupSocketMessageHandlers();
        log("✅ [connectToSocket] Message handlers set up");
      } catch (err) {
        error("❌ [connectToSocket] Error setting up WebSocket:", err);
        reject(err);
      }
    });
  }

  /**
   * Connect to WebRTC and set up input channel
   */
  async function connectToWebRTC() {
    return new Promise<void>((resolve, reject) => {
      try {
        log("🔧 [connectToWebRTC] Setting up WebRTC peer connection...");

        pc = new RTCPeerConnection({
          iceServers: [
            { urls: "stun:stun.cloudflare.com:3478" },
            {
              urls: "turn:relay1.expressturn.com:3478",
              username: "ef89RMU4SHUQMSOUU9",
              credential: "jvkMMnQxWX4Qrhe3",
            },
          ],
        });
        log("✅ [connectToWebRTC] RTCPeerConnection created");

        pc.onicecandidate = (event) => {
          if (event.candidate && socket?.readyState === WebSocket.OPEN) {
            log("🔧 [connectToWebRTC] Sending ICE candidate");
            socket.send(
              JSON.stringify({ type: "candidate", candidate: event.candidate })
            );
          }
        };

        // Set up connection state monitoring
        pc.onconnectionstatechange = () => {
          log(
            "🔧 [connectToWebRTC] WebRTC connection state:",
            pc?.connectionState
          );
          if (pc?.connectionState === "connected") {
            log(
              "✅ [connectToWebRTC] WebRTC connection established successfully"
            );
            resolve();
          } else if (pc?.connectionState === "failed") {
            error("❌ [connectToWebRTC] WebRTC connection failed");
            reject(new Error("WebRTC connection failed"));
          } else if (pc?.connectionState === "disconnected") {
            log("🔌 [connectToWebRTC] WebRTC connection disconnected");
          }
        };

        // Set up ICE connection state monitoring
        pc.oniceconnectionstatechange = () => {
          log(
            "🔧 [connectToWebRTC] ICE connection state:",
            pc?.iceConnectionState
          );
        };

        log("✅ [connectToWebRTC] WebRTC peer connection setup completed");

        // Don't create input channel immediately - wait for signaling
        // The input channel will be created when we create an offer
        log(
          "✅ [connectToWebRTC] WebRTC setup completed, waiting for signaling..."
        );
        resolve();
      } catch (err) {
        error("❌ [connectToWebRTC] Error setting up WebRTC:", err);
        reject(err);
      }
    });
  }

  /**
   * Set up WebSocket message handlers
   */
  function setupSocketMessageHandlers() {
    socket?.addEventListener("message", async (event) => {
      let msg;
      try {
        msg = JSON.parse(event.data);
      } catch (err) {
        log(
          "❌ [setupSocketMessageHandlers] Failed to handle WebSocket message:",
          err
        );
        return;
        // ignore non json message (possible htmx)
      }
      switch (msg.type) {
        case "offer":
          await pc?.setRemoteDescription(new RTCSessionDescription(msg.offer));
          const answer = await pc?.createAnswer();
          await pc?.setLocalDescription(answer);
          socket?.send(JSON.stringify({ type: "answer", answer }));
          break;
        case "answer":
          await pc?.setRemoteDescription(new RTCSessionDescription(msg.answer));
          break;
        case "candidate":
          await pc?.addIceCandidate(new RTCIceCandidate(msg.candidate));
          break;
        case "ready":
          // Create input channel before creating offer so it's included in the offer
          log("🔧 [ready] Creating input channel before offer...");
          createInputChannel();

          const offer = await pc?.createOffer();
          await pc?.setLocalDescription(offer);
          socket?.send(JSON.stringify({ type: "offer", offer }));
          log("✅ [ready] Offer sent with input channel");
          break;
        case "interactivity-status":
          if (msg.status === "paused") {
            pauseFrameSending();
          } else if (msg.status === "enabled") {
            // Update crop box if provided
            // DO not update crop box here as it will be updated by the tensorflow model directly
            // if (msg.cropBox) {
            //   updateCropBox(msg.cropBox);
            // }
            resumeFrameSending();
          } else if (msg.status === "completed") {
            log("Captcha solved, stopping streaming");
            stopStreaming();
          }
          break;
      }
    });

    socket?.addEventListener("error", (e) => {
      error("WebSocket error:", e);
    });
  }

  function logTime(label: string) {
    const entry = performance.getEntriesByName(label).at(-1);
    if (entry) {
      console.log(`⏱️ ${label} took ${entry.duration.toFixed(2)} ms`);
    }
    // performance.clearMarks();
    // performance.clearMeasures();
  }

  function makeEven(n: number) {
    return Math.floor(n / 2) * 2;
  }

  async function startStreaming(viewPort: Viewport, cropBox: BoundingBox) {
    // Update frame dimensions based on viewport
    FRAME_WIDTH = viewPort.width;
    FRAME_HEIGHT = viewPort.height;
    TOTAL_PIXELS = FRAME_WIDTH * FRAME_HEIGHT;

    if (config.debug) {
      log(`Updated frame dimensions to ${FRAME_WIDTH}x${FRAME_HEIGHT}`);
    }

    // Get display media stream
    log("Requesting display media stream...");
    try {
      screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          frameRate: config.frameRate,
          width: viewPort.width,
          height: viewPort.height,
        },
        //@ts-ignore
        preferCurrentTab: true,
      });
      log("Display media stream obtained successfully");
    } catch (err) {
      throw new Error(
        `[kazeel][screen-cropper] Failed to get display media stream: ${err}`
      );
    }

    // Add track to peer connection
    //@ts-ignore MediaStream does not support MediaStreamGenerator, they are not defined on js dom (we created it)
    pc?.addTrack(croppedTrack, stream);
    updateCropBox(cropBox);
  }

  let inputChannel: RTCDataChannel | undefined;
  let currentInputBoxRects = [];

  function createInputChannel() {
    try {
      log("🔧 [createInputChannel] Creating input data channel...");
      inputChannel = pc?.createDataChannel("inputEvents", {
        ordered: true,
        maxRetransmits: 3,
      });

      if (inputChannel !== undefined) {
        inputChannel.onopen = () => {
          log("✅ [createInputChannel] Input channel opened successfully");
          log(
            "🔧 [createInputChannel] Input channel state:",
            inputChannel?.readyState
          );
        };

        inputChannel.onclose = () => {
          log("🔌 [createInputChannel] Input channel closed");
        };

        inputChannel.onerror = (errorEvent) => {
          error("❌ [createInputChannel] Input channel error:", errorEvent);
        };

        inputChannel.onmessage = (event) => {
          log("🔧 [createInputChannel] Input channel message:", event.data);
        };
      }
    } catch (err) {
      error("❌ [createInputChannel] Error creating input channel:", err);
    }
  }

  function stopStreaming() {
    log("Stopping stream");

    try {
      if (screenStream) {
        const tracks = screenStream.getTracks();
        if (tracks && tracks.length > 0) {
          log("Stopping " + tracks.length + " screen tracks");
          tracks.forEach((track) => {
            if (track.readyState === "live") {
              track.stop();
            }
          });
        }
        screenStream = null;
      }

      if (socket && socket.readyState === WebSocket.OPEN) {
        try {
          socket.close();
        } catch (socketErr) {
          throw new Error(
            `[kazeel][screen-cropper] Error closing socket: ${socketErr}`
          );
        }
      }

      if (pc) {
        try {
          pc.close();
        } catch (pcErr) {
          throw new Error(
            `[kazeel][screen-cropper] Error closing peer connection: ${pcErr}`
          );
        }
        pc = null;
      }

      socket = null;

      log("Streaming stopped successfully");
    } catch (err) {
      throw new Error(
        `[kazeel][screen-cropper] Error during streaming cleanup: ${err}`
      );
    }
  }
});
window.tabStreamer = {
  init,
  start,
  stopStreaming,
};
