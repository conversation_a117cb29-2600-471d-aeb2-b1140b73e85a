import { CDP } from "../../browser/simple-cdp";

class PersistentCDPError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly operation?: string
  ) {
    super(`[kazeel][persistent-cdp-controller] ${message}`);
    this.name = "PersistentCDPError";
  }
}

/**
 * Persistent CDP Controller - handles all CDP operations
 * Runs in a dedicated control tab to maintain persistent CDP connection
 * Communicates with target tab via cross-tab messaging
 */
(function () {
  const config = {
    debug: true,
  };

  let cdpClient: CDP | null = null;
  let sessionId: string | undefined = undefined;
  let isMouseDown = false;

  function log(...args: any[]) {
    if (config.debug) {
      console.log("[kazeel][persistent-cdp-controller]", ...args);
    }
  }

  function error(...args: any[]) {
    console.error("[kazeel][persistent-cdp-controller]", ...args);
  }

  /**
   * Initialize the persistent CDP controller
   * @param browserFullWsEndpoint - WebSocket endpoint for browser connection
   * @param targetId - Target ID to attach to (passed from connections-workflow)
   */
  async function init(
    browserFullWsEndpoint: string,
    targetId: string
  ): Promise<void> {
    log("Initializing persistent CDP controller");
    log("Connecting to CDP and attaching to target:", targetId);

    await connectToCDPAndAttachToTarget(browserFullWsEndpoint, targetId);
    log("CDP connection established with sessionId:", sessionId);
    log("Persistent CDP controller initialized successfully");
  }

  /**
   * Establishes connection to CDP and attaches to existing target
   */
  async function connectToCDPAndAttachToTarget(
    wsEndpoint: string,
    targetId: string
  ): Promise<void> {
    try {
      // Create CDP connection
      cdpClient = new CDP({ webSocketDebuggerUrl: wsEndpoint });

      log("Attaching to target:", targetId);
      const { sessionId: attachedSessionId } =
        await cdpClient.Target.attachToTarget({
          targetId,
          flatten: true,
        });

      sessionId = attachedSessionId;
      log("Successfully attached to target with sessionId:", sessionId);

      // Enable necessary domains
      await Promise.all([
        cdpClient.Runtime.enable(undefined, sessionId),
        cdpClient.Page.enable(undefined, sessionId),
      ]);
      await cdpClient.Page.setBypassCSP({ enabled: true }, sessionId);
      await cdpClient.Emulation.setDeviceMetricsOverride(
        {
          width: 1024,
          height: 768,
          deviceScaleFactor: 1,
          mobile: false,
        },
        sessionId
      );
      log("CDP domains enabled successfully");
    } catch (err) {
      const cdpError = new PersistentCDPError(
        `Failed to connect to CDP or attach to target: ${err}`,
        "CDP_CONNECTION_FAILED",
        "connectToCDPAndAttachToTarget"
      );
      error(cdpError.message, err);
      throw cdpError;
    }
  }
})();
