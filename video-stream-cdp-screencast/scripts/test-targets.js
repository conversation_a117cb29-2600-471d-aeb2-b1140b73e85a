#!/usr/bin/env node

const WebSocket = require('ws');
const http = require('http');

console.log('🎯 Testing CDP Target Discovery with Real Target IDs\n');

class TargetTester {
  constructor() {
    this.serverUrl = 'ws://localhost:3000';
    this.apiUrl = 'http://localhost:3000';
  }

  async testTargetDiscovery() {
    console.log('1. Testing server status...');
    
    try {
      // Check if server is running
      await this.checkServerStatus();
      console.log('✅ Server is running\n');
      
      // Test WebSocket connection
      console.log('2. Testing WebSocket connection...');
      await this.testWebSocketConnection();
      
      // Test API endpoints
      console.log('3. Testing API endpoints...');
      await this.testAPIEndpoints();
      
    } catch (error) {
      console.error('❌ Test failed:', error.message);
      process.exit(1);
    }
  }

  async checkServerStatus() {
    return new Promise((resolve, reject) => {
      const req = http.get(`${this.apiUrl}/health`, (res) => {
        if (res.statusCode === 200) {
          resolve();
        } else {
          reject(new Error(`Server returned ${res.statusCode}`));
        }
      });
      
      req.on('error', reject);
      req.setTimeout(5000, () => reject(new Error('Timeout')));
    });
  }

  async testWebSocketConnection() {
    return new Promise((resolve, reject) => {
      const ws = new WebSocket(this.serverUrl);
      let receivedWelcome = false;
      let receivedTargets = false;
      
      const timeout = setTimeout(() => {
        ws.close();
        reject(new Error('WebSocket test timeout'));
      }, 10000);
      
      ws.on('open', () => {
        console.log('   📡 WebSocket connected');
        
        // Identify as UI client
        ws.send(JSON.stringify({
          type: 'identify',
          clientType: 'ui',
          userAgent: 'Target Tester'
        }));
      });
      
      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          console.log(`   📨 Received: ${message.type}`);
          
          switch (message.type) {
            case 'welcome':
              receivedWelcome = true;
              console.log(`   ✅ Welcome message received (clientId: ${message.clientId})`);
              
              // Request targets
              ws.send(JSON.stringify({
                type: 'getTargets'
              }));
              break;
              
            case 'targets':
              receivedTargets = true;
              console.log(`   ✅ Targets received: ${message.targets.length} targets`);
              
              // Display target information
              message.targets.forEach((target, index) => {
                console.log(`   ${index + 1}. ID: ${target.id}`);
                console.log(`      Title: ${target.title}`);
                console.log(`      URL: ${target.url}`);
                console.log(`      Type: ${target.type}`);
                console.log('');
              });
              
              if (receivedWelcome && receivedTargets) {
                clearTimeout(timeout);
                ws.close();
                resolve();
              }
              break;
              
            case 'error':
              clearTimeout(timeout);
              ws.close();
              reject(new Error(`Server error: ${message.message}`));
              break;
          }
        } catch (error) {
          console.error('   ⚠️  Failed to parse message:', error);
        }
      });
      
      ws.on('error', (error) => {
        clearTimeout(timeout);
        reject(new Error(`WebSocket error: ${error.message}`));
      });
      
      ws.on('close', () => {
        console.log('   📡 WebSocket disconnected');
      });
    });
  }

  async testAPIEndpoints() {
    const endpoints = [
      '/api/status',
      '/api/targets'
    ];
    
    for (const endpoint of endpoints) {
      try {
        console.log(`   🔍 Testing ${endpoint}...`);
        const data = await this.makeAPIRequest(endpoint);
        console.log(`   ✅ ${endpoint} responded successfully`);
        
        if (endpoint === '/api/targets') {
          console.log(`   📊 API Targets: ${data.targets ? data.targets.length : 0} targets`);
          if (data.targets && data.targets.length > 0) {
            data.targets.forEach((target, index) => {
              console.log(`      ${index + 1}. ${target.id} - ${target.title}`);
            });
          }
        }
        
        if (endpoint === '/api/status') {
          console.log(`   📊 Status: ${data.status}`);
          console.log(`   📊 Clients: ${data.clients}`);
          if (data.browser) {
            console.log(`   📊 Browser running: ${data.browser.running}`);
            console.log(`   📊 Target tabs: ${data.browser.targetTabs}`);
          }
        }
        
      } catch (error) {
        console.log(`   ⚠️  ${endpoint} failed: ${error.message}`);
      }
    }
  }

  async makeAPIRequest(endpoint) {
    return new Promise((resolve, reject) => {
      const req = http.get(`${this.apiUrl}${endpoint}`, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          try {
            const parsed = JSON.parse(data);
            resolve(parsed);
          } catch (error) {
            reject(new Error(`Failed to parse JSON: ${error.message}`));
          }
        });
      });
      
      req.on('error', reject);
      req.setTimeout(5000, () => reject(new Error('Request timeout')));
    });
  }
}

// Run the test
const tester = new TargetTester();
tester.testTargetDiscovery()
  .then(() => {
    console.log('🎉 All tests completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Test suite failed:', error);
    process.exit(1);
  });
