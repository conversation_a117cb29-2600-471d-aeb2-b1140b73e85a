#!/usr/bin/env node

const express = require("express");
const WebSocket = require("ws");
const http = require("http");
const cors = require("cors");
const { v4: uuidv4 } = require("uuid");
const path = require("path");
require("dotenv").config();

const BrowserManager = require("./browser-manager");
const WebRTCSignaling = require("./webrtc-signaling");

class SignalingServer {
  constructor() {
    this.app = express();
    this.server = http.createServer(this.app);
    this.wss = new WebSocket.Server({ server: this.server });

    this.port = process.env.SIGNALING_PORT || 3000;
    this.debug = process.env.DEBUG === "true";

    // Component managers
    this.browserManager = new BrowserManager();
    this.webrtcSignaling = new WebRTCSignaling();

    // Connection tracking
    this.clients = new Map(); // clientId -> { ws, info }
    this.managerConnection = null; // CDP manager tab connection
    this.discoveredTargets = []; // Store targets discovered by CDP manager

    this.setupExpress();
    this.setupWebSocket();
    this.setupRoutes();
  }

  setupExpress() {
    this.app.use(cors());
    this.app.use(express.json());
    this.app.use(express.static(path.join(__dirname, "../client-ui")));

    // Request logging
    this.app.use((req, res, next) => {
      if (this.debug) {
        console.log(`${new Date().toISOString()} ${req.method} ${req.path}`);
      }
      next();
    });
  }

  setupWebSocket() {
    this.wss.on("connection", (ws, req) => {
      const clientId = uuidv4();
      const clientInfo = {
        id: clientId,
        ws: ws,
        type: "unknown",
        connectedAt: new Date(),
        lastSeen: new Date(),
      };

      this.log(`New WebSocket connection: ${clientId}`);

      // Send welcome message
      this.sendToClient(ws, {
        type: "welcome",
        clientId: clientId,
        serverTime: new Date().toISOString(),
      });

      ws.on("message", async (data) => {
        try {
          const message = JSON.parse(data.toString());
          await this.handleWebSocketMessage(clientId, message);
        } catch (error) {
          this.error(
            `Error handling WebSocket message from ${clientId}:`,
            error
          );
          this.sendToClient(ws, {
            type: "error",
            message: "Invalid message format",
          });
        }
      });

      ws.on("close", () => {
        this.handleClientDisconnect(clientId);
      });

      ws.on("error", (error) => {
        this.error(`WebSocket error for client ${clientId}:`, error);
        this.handleClientDisconnect(clientId);
      });

      this.clients.set(clientId, clientInfo);
    });
  }

  setupRoutes() {
    // Browser management API
    this.app.get("/api/status", (req, res) => {
      res.json({
        status: "running",
        clients: this.clients.size,
        browser: this.browserManager.getStatus(),
        uptime: process.uptime(),
      });
    });

    this.app.post("/api/browser/launch", async (req, res) => {
      try {
        const result = await this.browserManager.launch();
        res.json({ success: true, ...result });
      } catch (error) {
        this.error("Failed to launch browser:", error);
        res.status(500).json({ success: false, error: error.message });
      }
    });

    this.app.post("/api/browser/close", async (req, res) => {
      try {
        await this.browserManager.close();
        res.json({ success: true });
      } catch (error) {
        this.error("Failed to close browser:", error);
        res.status(500).json({ success: false, error: error.message });
      }
    });

    this.app.get("/api/targets", async (req, res) => {
      try {
        const targets = await this.browserManager.getTargets();
        res.json({ success: true, targets });
      } catch (error) {
        this.error("Failed to get targets:", error);
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // Health check
    this.app.get("/health", (req, res) => {
      res.json({ status: "healthy", timestamp: new Date().toISOString() });
    });
  }

  async handleWebSocketMessage(clientId, message) {
    const client = this.clients.get(clientId);
    if (!client) return;

    client.lastSeen = new Date();
    this.log(`Message from ${clientId}:`, message.type);

    switch (message.type) {
      // case "identify":
      //   await this.handleClientIdentify(clientId, message);
      //   break;

      case "getTargets":
        await this.handleGetTargets(clientId);
        break;

      case "selectTarget":
        await this.handleSelectTarget(clientId, message);
        break;

      case "webrtc-offer":
      case "webrtc-answer":
      case "webrtc-ice-candidate":
        await this.handleWebRTCSignaling(clientId, message);
        break;

      case "managerReady":
        await this.handleManagerReady(clientId, message);
        break;

      case "targetsDiscovered":
        await this.handleTargetsDiscovered(clientId, message);
        break;

      default:
        this.log(`Unknown message type: ${message.type}`);
    }
  }

  async handleClientIdentify(clientId, message) {
    const client = this.clients.get(clientId);
    if (!client) return;

    client.type = message.clientType || "ui";
    client.userAgent = message.userAgent;

    if (client.type === "manager") {
      this.managerConnection = client;
      this.log(`CDP Manager connected: ${clientId}`);

      // Request target discovery
      this.sendToClient(client.ws, {
        type: "discoverTargets",
      });
    }

    this.log(`Client identified: ${clientId} as ${client.type}`);
  }

  async handleGetTargets(clientId) {
    try {
      // Use discovered targets from CDP manager if available, otherwise fallback to browser manager
      const targets =
        this.discoveredTargets.length > 0
          ? this.discoveredTargets
          : await this.browserManager.getTargets();

      const client = this.clients.get(clientId);
      if (client) {
        this.sendToClient(client.ws, {
          type: "targets",
          targets: targets,
        });
      }
    } catch (error) {
      this.error("Failed to get targets:", error);
    }
  }

  async handleSelectTarget(clientId, message) {
    if (this.managerConnection) {
      this.sendToClient(this.managerConnection.ws, {
        type: "selectTarget",
        targetId: message.targetId,
        clientId: clientId,
      });
    }
  }

  async handleWebRTCSignaling(clientId, message) {
    // Forward WebRTC signaling between client and manager
    if (message.targetType === "manager" && this.managerConnection) {
      this.sendToClient(this.managerConnection.ws, {
        ...message,
        fromClientId: clientId,
      });
    } else {
      // Forward to specific client
      const targetClient = this.clients.get(message.targetClientId);
      if (targetClient) {
        this.sendToClient(targetClient.ws, message);
      }
    }
  }

  async handleManagerReady(clientId, message) {
    this.log("CDP Manager is ready");
    // Notify all UI clients that manager is ready
    this.broadcastToUIClients({
      type: "managerReady",
      capabilities: message.capabilities,
    });
  }

  async handleTargetsDiscovered(clientId, message) {
    this.log(`Targets discovered: ${message.targets.length}`);

    // Store the discovered targets with real CDP target IDs
    this.discoveredTargets = message.targets;

    // Broadcast targets to all UI clients
    this.broadcastToUIClients({
      type: "targets",
      targets: message.targets,
    });
  }

  handleClientDisconnect(clientId) {
    const client = this.clients.get(clientId);
    if (client) {
      this.log(`Client disconnected: ${clientId} (${client.type})`);

      if (client === this.managerConnection) {
        this.managerConnection = null;
        this.log("CDP Manager disconnected");

        // Notify UI clients
        this.broadcastToUIClients({
          type: "managerDisconnected",
        });
      }

      this.clients.delete(clientId);
    }
  }

  sendToClient(ws, message) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  broadcastToUIClients(message) {
    this.clients.forEach((client) => {
      if (client.type === "ui" && client.ws.readyState === WebSocket.OPEN) {
        this.sendToClient(client.ws, message);
      }
    });
  }

  log(...args) {
    if (this.debug) {
      console.log(`[SignalingServer]`, ...args);
    }
  }

  error(...args) {
    console.error(`[SignalingServer ERROR]`, ...args);
  }

  async start() {
    return new Promise((resolve) => {
      this.server.listen(this.port, () => {
        console.log(`🚀 Signaling Server running on port ${this.port}`);
        console.log(`📊 Status: http://localhost:${this.port}/api/status`);
        console.log(`🌐 Client UI: http://localhost:${this.port}`);
        resolve();
      });
    });
  }

  async stop() {
    await this.browserManager.close();
    this.server.close();
    console.log("Signaling Server stopped");
  }
}

// Start server if run directly
if (require.main === module) {
  const server = new SignalingServer();

  server.start().catch((error) => {
    console.error("Failed to start server:", error);
    process.exit(1);
  });

  // Graceful shutdown
  process.on("SIGINT", async () => {
    console.log("\n🛑 Shutting down...");
    await server.stop();
    process.exit(0);
  });
}

module.exports = SignalingServer;
