{"name": "video-stream-demo", "version": "1.0.0", "type": "module", "description": "Chrome DevTools Protocol + Extension Video Streaming Demo", "main": "demo.js", "scripts": {"start": "node demo.js", "start-server": "node streaming-server/server.js", "start-puppeteer": "node puppeteer-controller/demo.js", "start-puppeteer-auto": "node puppeteer-controller/demo.js --auto", "dev": "concurrently \"npm run start-server\" \"npm run start-puppeteer\"", "test": "node test-setup.js", "test-setup": "node test-setup.js"}, "dependencies": {"puppeteer": "^24.10.0", "ws": "^8.18.3"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["chrome", "extension", "video", "streaming", "cdp", "puppeteer", "websocket"], "author": "", "license": "MIT"}