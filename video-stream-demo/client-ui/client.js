// Multi-Tab Video Stream Client
class VideoStreamClient {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.autoReconnect = true;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 3000;
    this.debugMode = false;
    this.messageLog = [];
    this.streamStats = {
      bytesReceived: 0,
      framesReceived: 0,
      lastFrameTime: 0,
    };

    this.initializeElements();
    this.setupEventListeners();
    this.connect();
  }

  initializeElements() {
    // Connection status elements
    this.connectionStatus = document.getElementById("connectionStatus");
    this.statusIndicator =
      this.connectionStatus.querySelector(".status-indicator");
    this.statusText = this.connectionStatus.querySelector(".status-text");

    // Control buttons
    this.githubBtn = document.getElementById("switchToGithub");
    this.googleBtn = document.getElementById("switchToGoogle");
    this.reconnectBtn = document.getElementById("reconnectBtn");
    this.clearDebugBtn = document.getElementById("clearDebugBtn");
    this.downloadLogBtn = document.getElementById("downloadLogBtn");

    // Settings
    this.autoReconnectCheckbox = document.getElementById("autoReconnect");
    this.showDebugCheckbox = document.getElementById("showDebug");

    // Status info
    this.clientCount = document.getElementById("clientCount");
    this.streamCount = document.getElementById("streamCount");
    this.currentStream = document.getElementById("currentStream");

    // Video elements
    this.videoPlayer = document.getElementById("videoPlayer");
    this.videoSource = document.getElementById("videoSource");
    this.videoOverlay = document.getElementById("videoOverlay");
    this.overlayText = document.getElementById("overlayText");

    // Stream info
    this.streamSource = document.getElementById("streamSource");
    this.streamResolution = document.getElementById("streamResolution");
    this.streamFrameRate = document.getElementById("streamFrameRate");
    this.streamBitrate = document.getElementById("streamBitrate");

    // Debug panel
    this.debugPanel = document.getElementById("debugPanel");
    this.debugMessages = document.getElementById("debugMessages");
    this.debugStats = document.getElementById("debugStats");
  }

  setupEventListeners() {
    // Control buttons
    this.githubBtn.addEventListener("click", () => this.switchStream("github"));
    this.googleBtn.addEventListener("click", () => this.switchStream("google"));
    this.reconnectBtn.addEventListener("click", () => this.reconnect());
    this.clearDebugBtn.addEventListener("click", () => this.clearDebugLog());
    this.downloadLogBtn.addEventListener("click", () => this.downloadLog());

    // Settings
    this.autoReconnectCheckbox.addEventListener("change", (e) => {
      this.autoReconnect = e.target.checked;
    });

    this.showDebugCheckbox.addEventListener("change", (e) => {
      this.debugMode = e.target.checked;
      this.debugPanel.style.display = this.debugMode ? "block" : "none";
    });

    // Video events
    this.videoPlayer.addEventListener("loadedmetadata", () => {
      this.updateStreamInfo();
      this.hideVideoOverlay();
    });

    this.videoPlayer.addEventListener("error", (e) => {
      this.showVideoOverlay("Video playback error");
      this.logDebug("Video error: " + e.message);
    });
  }

  connect() {
    try {
      this.updateConnectionStatus("connecting", "Connecting...");
      this.ws = new WebSocket("ws://localhost:8080");

      this.ws.onopen = () => {
        this.onConnected();
      };

      this.ws.onmessage = (event) => {
        this.onMessage(event);
      };

      this.ws.onclose = () => {
        this.onDisconnected();
      };

      this.ws.onerror = (error) => {
        this.onError(error);
      };
    } catch (error) {
      this.onError(error);
    }
  }

  onConnected() {
    this.isConnected = true;
    this.reconnectAttempts = 0;
    this.updateConnectionStatus("connected", "Connected");
    this.logDebug("Connected to streaming server");

    // Request server status
    this.sendMessage({ type: "getStatus" });
  }

  onMessage(event) {
    try {
      const message = JSON.parse(event.data);
      this.handleMessage(message);
    } catch (error) {
      this.logDebug("Error parsing message: " + error.message);
    }
  }

  onDisconnected() {
    this.isConnected = false;
    this.updateConnectionStatus("disconnected", "Disconnected");
    this.logDebug("Disconnected from streaming server");

    // Disable controls
    this.githubBtn.disabled = true;
    this.googleBtn.disabled = true;

    // Show overlay
    this.showVideoOverlay("Connection lost");

    // Auto reconnect
    if (
      this.autoReconnect &&
      this.reconnectAttempts < this.maxReconnectAttempts
    ) {
      setTimeout(() => {
        this.reconnectAttempts++;
        this.logDebug(
          `Reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`
        );
        this.connect();
      }, this.reconnectDelay);
    }
  }

  onError(error) {
    this.logDebug("WebSocket error: " + error.message);
    this.updateConnectionStatus("disconnected", "Connection Error");
  }

  handleMessage(message) {
    this.logDebug(`Received: ${message.type}`);

    switch (message.type) {
      case "welcome":
        this.handleWelcome(message);
        break;
      case "videoStream":
        this.handleVideoStream(message);
        break;
      case "streamSwitched":
        this.handleStreamSwitch(message);
        break;
      case "captureStarted":
        this.handleCaptureStarted(message);
        break;
      case "captureStopped":
        this.handleCaptureStopped(message);
        break;
      case "serverStatus":
        this.handleServerStatus(message);
        break;
      case "ping":
        this.sendMessage({ type: "pong" });
        break;
      default:
        this.logDebug(`Unknown message type: ${message.type}`);
    }
  }

  handleWelcome(message) {
    this.logDebug(`Welcome message received. Client ID: ${message.clientId}`);

    // Enable controls
    this.githubBtn.disabled = false;
    this.googleBtn.disabled = false;

    // Update status
    if (message.serverStatus) {
      this.updateServerStatus(message.serverStatus);
    }
  }

  handleVideoStream(message) {
    // Convert array back to Uint8Array and create blob
    const uint8Array = new Uint8Array(message.data);
    const blob = new Blob([uint8Array], { type: "video/webm" });

    // Create object URL and set as video source
    const videoUrl = URL.createObjectURL(blob);
    this.videoSource.src = videoUrl;
    this.videoPlayer.load();

    // Update stats
    this.streamStats.bytesReceived += uint8Array.length;
    this.streamStats.framesReceived++;
    this.streamStats.lastFrameTime = Date.now();

    this.updateDebugStats();
    this.logDebug(`Video frame received: ${uint8Array.length} bytes`);
  }

  handleStreamSwitch(message) {
    this.logDebug(`Stream switched to tab: ${message.tabId}`);
    this.currentStream.textContent = this.getTabName(message.tabId);
    this.streamSource.textContent = this.getTabName(message.tabId);
  }

  handleCaptureStarted(message) {
    this.logDebug(`Capture started for tab: ${message.tabId}`);
    this.showVideoOverlay(
      `Starting capture from ${this.getTabName(message.tabId)}...`
    );
  }

  handleCaptureStopped(message) {
    this.logDebug(`Capture stopped for tab: ${message.tabId}`);
    this.showVideoOverlay("Capture stopped");
  }

  handleServerStatus(message) {
    this.updateServerStatus(message.status);
  }

  updateServerStatus(status) {
    this.clientCount.textContent = status.clients || 0;
    this.streamCount.textContent = status.activeStreams?.length || 0;
    this.currentStream.textContent = status.currentStreamTab
      ? this.getTabName(status.currentStreamTab)
      : "None";
  }

  getTabName(tabId) {
    if (typeof tabId === "string") {
      return tabId.charAt(0).toUpperCase() + tabId.slice(1);
    }
    return `Tab ${tabId}`;
  }

  switchStream(tabName) {
    if (!this.isConnected) {
      this.logDebug("Cannot switch stream: not connected");
      return;
    }

    this.sendMessage({
      type: "switchStream",
      tabId: tabName,
    });

    this.logDebug(`Requesting switch to ${tabName}`);
    this.showVideoOverlay(`Switching to ${tabName}...`);
  }

  sendMessage(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  reconnect() {
    this.reconnectAttempts = 0;
    if (this.ws) {
      this.ws.close();
    }
    this.connect();
  }

  updateConnectionStatus(status, text) {
    this.statusIndicator.className = `status-indicator ${status}`;
    this.statusText.textContent = text;
  }

  showVideoOverlay(text) {
    this.overlayText.textContent = text;
    this.videoOverlay.style.display = "flex";
  }

  hideVideoOverlay() {
    this.videoOverlay.style.display = "none";
  }

  updateStreamInfo() {
    if (this.videoPlayer.videoWidth && this.videoPlayer.videoHeight) {
      this.streamResolution.textContent = `${this.videoPlayer.videoWidth}x${this.videoPlayer.videoHeight}`;
    }
  }

  logDebug(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] ${message}`;

    this.messageLog.push(logEntry);

    if (this.debugMode) {
      this.debugMessages.textContent += logEntry + "\n";
      this.debugMessages.scrollTop = this.debugMessages.scrollHeight;
    }

    console.log(logEntry);
  }

  updateDebugStats() {
    if (this.debugMode) {
      const stats = `
Bytes Received: ${this.streamStats.bytesReceived}
Frames Received: ${this.streamStats.framesReceived}
Last Frame: ${new Date(this.streamStats.lastFrameTime).toLocaleTimeString()}
Connection: ${this.isConnected ? "Connected" : "Disconnected"}
Reconnect Attempts: ${this.reconnectAttempts}
            `.trim();

      this.debugStats.textContent = stats;
    }
  }

  clearDebugLog() {
    this.messageLog = [];
    this.debugMessages.textContent = "";
    this.streamStats = {
      bytesReceived: 0,
      framesReceived: 0,
      lastFrameTime: 0,
    };
    this.updateDebugStats();
  }

  downloadLog() {
    const logContent = this.messageLog.join("\n");
    const blob = new Blob([logContent], { type: "text/plain" });
    const url = URL.createObjectURL(blob);

    const a = document.createElement("a");
    a.href = url;
    a.download = `video-stream-log-${new Date()
      .toISOString()
      .slice(0, 19)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
}

// Initialize client when page loads
document.addEventListener("DOMContentLoaded", () => {
  new VideoStreamClient();
});
