/* Multi-Tab Video Stream Viewer Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header h1 {
    color: #2c3e50;
    font-size: 2rem;
    font-weight: 600;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-indicator.connected {
    background: #27ae60;
}

.status-indicator.disconnected {
    background: #e74c3c;
}

.status-indicator.connecting {
    background: #f39c12;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Controls Panel */
.controls-panel {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.control-group {
    margin-bottom: 20px;
}

.control-group:last-child {
    margin-bottom: 0;
}

.control-group h3 {
    color: #2c3e50;
    margin-bottom: 12px;
    font-size: 1.1rem;
}

.button-group {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.control-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 160px;
}

.facebook-btn {
    background: #1877f2;
    color: white;
}

.facebook-btn:hover:not(:disabled) {
    background: #166fe5;
    transform: translateY(-2px);
}

.google-btn {
    background: #4285f4;
    color: white;
}

.google-btn:hover:not(:disabled) {
    background: #3367d6;
    transform: translateY(-2px);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Settings */
.settings-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 8px 0;
}

.settings-row label {
    font-weight: 500;
    color: #555;
}

/* Status Info */
.status-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.status-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.status-row:last-child {
    margin-bottom: 0;
}

.status-row .label {
    font-weight: 500;
    color: #666;
}

/* Video Container */
.video-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.video-wrapper {
    position: relative;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto 20px;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

#videoPlayer {
    width: 100%;
    height: auto;
    min-height: 400px;
    display: block;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.overlay-content {
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #333;
    border-top: 4px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Stream Info */
.stream-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-row .label {
    font-weight: 500;
    color: #666;
}

/* Debug Panel */
.debug-panel {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.debug-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.debug-section h4 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1rem;
}

.debug-messages {
    background: #2c3e50;
    color: #ecf0f1;
    padding: 15px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
}

.debug-stats {
    background: #34495e;
    color: #ecf0f1;
    padding: 15px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    height: 200px;
    overflow-y: auto;
}

/* Footer */
.footer {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.footer-content p {
    color: #666;
    font-size: 14px;
}

.footer-links {
    display: flex;
    gap: 10px;
}

.footer-btn {
    padding: 8px 16px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.footer-btn:hover {
    background: #f8f9fa;
    border-color: #bbb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .header h1 {
        font-size: 1.5rem;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .control-btn {
        min-width: auto;
        width: 100%;
    }
    
    .debug-content {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
    }
    
    .stream-info {
        grid-template-columns: 1fr;
    }
}
