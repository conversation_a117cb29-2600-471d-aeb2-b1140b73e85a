<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Multi-Tab Video Stream Viewer</title>
    <link rel="stylesheet" href="style.css" />
  </head>
  <body>
    <div class="container">
      <header class="header">
        <h1>🎥 Multi-Tab Video Stream Viewer</h1>
        <div class="connection-status" id="connectionStatus">
          <span class="status-indicator disconnected"></span>
          <span class="status-text">Disconnected</span>
        </div>
      </header>

      <div class="controls-panel">
        <div class="control-group">
          <h3>📺 Stream Controls</h3>
          <div class="button-group">
            <button
              id="switchToGithub"
              class="control-btn facebook-btn"
              disabled
            >
              📘 Switch to Github
            </button>
            <button id="switchToGoogle" class="control-btn google-btn" disabled>
              🔍 Switch to Google
            </button>
          </div>
        </div>

        <div class="control-group">
          <h3>⚙️ Settings</h3>
          <div class="settings-row">
            <label for="autoReconnect">Auto Reconnect:</label>
            <input type="checkbox" id="autoReconnect" checked />
          </div>
          <div class="settings-row">
            <label for="showDebug">Show Debug Info:</label>
            <input type="checkbox" id="showDebug" />
          </div>
        </div>

        <div class="control-group">
          <h3>📊 Server Status</h3>
          <div class="status-info">
            <div class="status-row">
              <span class="label">Connected Clients:</span>
              <span id="clientCount">0</span>
            </div>
            <div class="status-row">
              <span class="label">Active Streams:</span>
              <span id="streamCount">0</span>
            </div>
            <div class="status-row">
              <span class="label">Current Stream:</span>
              <span id="currentStream">None</span>
            </div>
          </div>
        </div>
      </div>

      <div class="video-container">
        <div class="video-wrapper">
          <video id="videoPlayer" controls autoplay muted>
            <source id="videoSource" type="video/webm" />
            Your browser does not support the video tag.
          </video>
          <div class="video-overlay" id="videoOverlay">
            <div class="overlay-content">
              <div class="loading-spinner"></div>
              <p id="overlayText">Waiting for video stream...</p>
            </div>
          </div>
        </div>

        <div class="stream-info">
          <div class="info-row">
            <span class="label">Stream Source:</span>
            <span id="streamSource">None</span>
          </div>
          <div class="info-row">
            <span class="label">Resolution:</span>
            <span id="streamResolution">N/A</span>
          </div>
          <div class="info-row">
            <span class="label">Frame Rate:</span>
            <span id="streamFrameRate">N/A</span>
          </div>
          <div class="info-row">
            <span class="label">Bitrate:</span>
            <span id="streamBitrate">N/A</span>
          </div>
        </div>
      </div>

      <div class="debug-panel" id="debugPanel" style="display: none">
        <h3>🐛 Debug Information</h3>
        <div class="debug-content">
          <div class="debug-section">
            <h4>WebSocket Messages</h4>
            <div id="debugMessages" class="debug-messages"></div>
          </div>
          <div class="debug-section">
            <h4>Stream Statistics</h4>
            <div id="debugStats" class="debug-stats"></div>
          </div>
        </div>
      </div>

      <footer class="footer">
        <div class="footer-content">
          <p>Chrome DevTools Protocol + Extension Video Streaming Demo</p>
          <div class="footer-links">
            <button id="reconnectBtn" class="footer-btn">🔄 Reconnect</button>
            <button id="clearDebugBtn" class="footer-btn">
              🗑️ Clear Debug
            </button>
            <button id="downloadLogBtn" class="footer-btn">
              💾 Download Log
            </button>
          </div>
        </div>
      </footer>
    </div>

    <script src="client.js"></script>
  </body>
</html>
