import CDPController from "./cdp-controller.js";

async function runDemo() {
  const controller = new CDPController();

  try {
    console.log("🎬 Starting Video Stream Capture Demo");
    console.log("=====================================");

    // Initialize browser and extension
    await controller.initialize();

    // Open target tabs
    await controller.openTargetTabs();

    // Inject CDP commands
    await controller.injectCDPCommands();

    // Enable network monitoring
    await controller.monitorNetworkActivity("github");
    await controller.monitorNetworkActivity("google");

    console.log("\n🎯 Demo Setup Complete!");
    console.log("📋 Available Commands:");
    console.log("  1. Switch to github tab");
    console.log("  2. Switch to Google tab");
    console.log("  3. Open extension popup");
    console.log("  4. Execute custom script");
    console.log("  5. Exit demo");

    // Interactive demo loop
    await runInteractiveDemo(controller);
  } catch (error) {
    console.error("❌ Demo error:", error);
  } finally {
    await controller.cleanup();
  }
}

async function runInteractiveDemo(controller) {
  const readline = await import("readline");
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  const prompt = (question) =>
    new Promise((resolve) => {
      rl.question(question, resolve);
    });

  while (true) {
    console.log("\n" + "=".repeat(50));
    const choice = await prompt("Enter command (1-5): ");

    switch (choice.trim()) {
      case "1":
        await controller.switchToTab("github");
        await controller.waitForUserInput(
          "Github tab is now active. Press Enter to continue..."
        );
        break;

      case "2":
        await controller.switchToTab("google");
        await controller.waitForUserInput(
          "Google tab is now active. Press Enter to continue..."
        );
        break;

      case "3":
        const popup = await controller.openExtensionPopup();
        if (popup) {
          await controller.waitForUserInput(
            "Extension popup opened. Press Enter to continue..."
          );
        }
        break;

      case "4":
        const script = await prompt("Enter JavaScript to execute: ");
        const tabName = await prompt("Execute in which tab? (Github/google): ");
        const result = await controller.executeInTab(tabName, script);
        console.log("Script result:", result);
        break;

      case "5":
        console.log("👋 Exiting demo...");
        rl.close();
        return;

      default:
        console.log("❌ Invalid choice. Please enter 1-5.");
    }
  }
}

// Auto-run demo
async function autoDemo() {
  const controller = new CDPController();

  try {
    console.log("🤖 Running Automated Demo");
    console.log("==========================");

    // Initialize
    await controller.initialize();
    await controller.openTargetTabs();
    await controller.injectCDPCommands();

    console.log("\n⏱️  Starting automated sequence...");

    // Demonstrate tab switching
    console.log("1️⃣  Switching to github...");
    await controller.switchToTab("github");
    await new Promise((resolve) => setTimeout(resolve, 3000));

    console.log("2️⃣  Switching to Google...");
    await controller.switchToTab("google");
    await new Promise((resolve) => setTimeout(resolve, 3000));

    console.log("3️⃣  Opening extension popup...");
    await controller.openExtensionPopup();
    await new Promise((resolve) => setTimeout(resolve, 3000));

    console.log("4️⃣  Executing test script in github...");
    const result = await controller.executeInTab("github", () => {
      return {
        title: document.title,
        url: window.location.href,
        timestamp: new Date().toISOString(),
      };
    });
    console.log("📊 Script result:", result);

    console.log("\n✅ Automated demo complete!");
    console.log("🔧 Browser will remain open for manual testing...");

    await controller.waitForUserInput(
      "\nPress Enter to close browser and exit..."
    );
  } catch (error) {
    console.error("❌ Auto demo error:", error);
  } finally {
    await controller.cleanup();
  }
}

// Check command line arguments
const args = process.argv.slice(2);
if (args.includes("--auto")) {
  autoDemo();
} else {
  runDemo();
}
