import puppeteer from "puppeteer";
import path from "path";
import { fileURLToPath } from "url";
import { dirname } from "path";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class CDPController {
  constructor() {
    this.browser = null;
    this.pages = {};
    this.extensionId = null;
  }

  async initialize() {
    console.log("🚀 Initializing CDP Controller...");

    // Path to the Chrome extension
    const extensionPath = path.join(__dirname, "../chrome-extension");

    // Launch Chrome with CDP and extension
    this.browser = await puppeteer.launch({
      headless: false,
      devtools: false,
      args: [
        "--remote-debugging-port=9222",
        "--remote-allow-origins=*",
        "--auto-accept-this-tab-capture",
        "--use-fake-ui-for-media-stream",
        "--use-fake-device-for-media-stream",
        "--allow-running-insecure-content",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
        `--load-extension=${extensionPath}`,
        "--no-sandbox",
        "--disable-setuid-sandbox",
      ],
      defaultViewport: null,
    });

    console.log("✅ Chrome browser launched with CDP enabled");

    // Wait a moment for extension to load
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Get extension ID
    await this.getExtensionId();

    return this.browser;
  }

  async getExtensionId() {
    try {
      const targets = await this.browser.targets();
      const extensionTarget = targets.find(
        (target) =>
          target.type() === "background_page" &&
          target.url().includes("chrome-extension://")
      );

      if (extensionTarget) {
        const url = extensionTarget.url();
        this.extensionId = url.split("://")[1].split("/")[0];
        console.log(`📦 Extension loaded with ID: ${this.extensionId}`);
      } else {
        console.warn("⚠️  Extension not found in targets");
      }
    } catch (error) {
      console.error("❌ Error getting extension ID:", error);
    }
  }

  async openTargetTabs() {
    console.log("🌐 Opening target tabs...");

    // Open github tab
    const githubPage = await this.browser.newPage();
    await githubPage.goto("https://www.github.com", {
      waitUntil: "networkidle2",
      timeout: 30000,
    });
    this.pages.github = githubPage;
    console.log("📘 github tab opened");

    // Open Google tab
    const googlePage = await this.browser.newPage();
    await googlePage.goto("https://www.google.com", {
      waitUntil: "networkidle2",
      timeout: 30000,
    });
    this.pages.google = googlePage;
    console.log("🔍 Google tab opened");

    // Get tab IDs for the extension
    await this.getTabIds();

    return {
      github: githubPage,
      google: googlePage,
    };
  }

  async getTabIds() {
    try {
      const pages = await this.browser.pages();

      for (const page of pages) {
        const url = page.url();
        if (url.includes("github.com")) {
          const target = page.target();
          this.pages.githubTabId = target._targetId;
          console.log(`📘 github tab ID: ${this.pages.githubTabId}`);
        } else if (url.includes("google.com")) {
          const target = page.target();
          this.pages.googleTabId = target._targetId;
          console.log(`🔍 Google tab ID: ${this.pages.googleTabId}`);
        }
      }
    } catch (error) {
      console.error("❌ Error getting tab IDs:", error);
    }
  }

  async openExtensionPopup() {
    if (!this.extensionId) {
      console.warn("⚠️  Extension ID not available");
      return null;
    }

    try {
      const extensionPage = await this.browser.newPage();
      await extensionPage.goto(
        `chrome-extension://${this.extensionId}/popup.html`
      );
      console.log("🔧 Extension popup opened");
      return extensionPage;
    } catch (error) {
      console.error("❌ Error opening extension popup:", error);
      return null;
    }
  }

  async injectCDPCommands() {
    console.log("💉 Injecting CDP commands...");

    try {
      // Enable Runtime domain for all pages
      for (const [name, page] of Object.entries(this.pages)) {
        if (page && typeof page.target === "function") {
          const client = await page.target().createCDPSession();
          await client.send("Runtime.enable");
          await client.send("Page.enable");
          console.log(`✅ CDP enabled for ${name} page`);
        }
      }
    } catch (error) {
      console.error("❌ Error injecting CDP commands:", error);
    }
  }

  async switchToTab(tabName) {
    const page = this.pages[tabName];
    if (page) {
      await page.bringToFront();
      console.log(`🔄 Switched to ${tabName} tab`);
      return page;
    } else {
      console.warn(`⚠️  Tab ${tabName} not found`);
      return null;
    }
  }

  async executeInTab(tabName, script) {
    const page = this.pages[tabName];
    if (page) {
      try {
        const result = await page.evaluate(script);
        console.log(`✅ Script executed in ${tabName} tab`);
        return result;
      } catch (error) {
        console.error(`❌ Error executing script in ${tabName}:`, error);
        return null;
      }
    } else {
      console.warn(`⚠️  Tab ${tabName} not found`);
      return null;
    }
  }

  async monitorNetworkActivity(tabName) {
    const page = this.pages[tabName];
    if (page) {
      await page.setRequestInterception(true);

      page.on("request", (request) => {
        console.log(
          `🌐 ${tabName} Request: ${request.method()} ${request.url()}`
        );
        request.continue();
      });

      page.on("response", (response) => {
        console.log(
          `📡 ${tabName} Response: ${response.status()} ${response.url()}`
        );
      });

      console.log(`👁️  Network monitoring enabled for ${tabName}`);
    }
  }

  async cleanup() {
    console.log("🧹 Cleaning up CDP Controller...");

    if (this.browser) {
      await this.browser.close();
      console.log("✅ Browser closed");
    }
  }

  // Utility method to wait for user input
  async waitForUserInput(message = "Press Enter to continue...") {
    const readline = await import("readline");
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    return new Promise((resolve) => {
      rl.question(message, () => {
        rl.close();
        resolve();
      });
    });
  }
}

export default CDPController;
