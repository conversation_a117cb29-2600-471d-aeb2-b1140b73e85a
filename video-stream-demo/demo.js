#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import readline from 'readline';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const prompt = (question) => new Promise(resolve => {
  rl.question(question, resolve);
});

class DemoOrchestrator {
  constructor() {
    this.processes = new Map();
    this.isRunning = false;
  }

  async start() {
    console.log('🎬 Chrome DevTools Protocol + Extension Video Streaming Demo');
    console.log('============================================================');
    console.log('');
    console.log('This demo will:');
    console.log('1. Start a WebSocket streaming server');
    console.log('2. Launch Chrome with CDP enabled and load the extension');
    console.log('3. Open Facebook and Google tabs');
    console.log('4. Provide a client UI to view and switch between streams');
    console.log('');

    const choice = await prompt('Choose demo mode:\n1. Interactive (manual control)\n2. Automated (scripted demo)\n3. Server only\n4. Exit\n\nEnter choice (1-4): ');

    switch (choice.trim()) {
      case '1':
        await this.runInteractiveDemo();
        break;
      case '2':
        await this.runAutomatedDemo();
        break;
      case '3':
        await this.runServerOnly();
        break;
      case '4':
        console.log('👋 Goodbye!');
        process.exit(0);
        break;
      default:
        console.log('❌ Invalid choice. Please run the demo again.');
        process.exit(1);
    }
  }

  async runInteractiveDemo() {
    console.log('\n🎮 Starting Interactive Demo...');
    
    // Start streaming server
    await this.startStreamingServer();
    
    // Wait a moment for server to start
    await this.sleep(2000);
    
    // Start Puppeteer controller
    await this.startPuppeteerController();
    
    console.log('\n✅ Demo is running!');
    console.log('📋 What you can do now:');
    console.log('  • Open http://localhost:8080 in your browser to view the client UI');
    console.log('  • Use the Chrome extension popup to start/stop video capture');
    console.log('  • Switch between Facebook and Google tabs using the client UI');
    console.log('  • Monitor the streaming server logs in this terminal');
    console.log('');
    
    await prompt('Press Enter to stop the demo...');
    await this.cleanup();
  }

  async runAutomatedDemo() {
    console.log('\n🤖 Starting Automated Demo...');
    
    // Start streaming server
    await this.startStreamingServer();
    
    // Wait for server to start
    await this.sleep(2000);
    
    // Start Puppeteer controller in auto mode
    await this.startPuppeteerController(['--auto']);
    
    console.log('\n✅ Automated demo is running!');
    console.log('📋 The demo will:');
    console.log('  • Automatically open tabs and demonstrate features');
    console.log('  • You can still open http://localhost:8080 to view the client UI');
    console.log('  • The browser will remain open for manual testing');
    console.log('');
    
    await prompt('Press Enter to stop the demo...');
    await this.cleanup();
  }

  async runServerOnly() {
    console.log('\n🖥️  Starting Server Only Mode...');
    
    await this.startStreamingServer();
    
    console.log('\n✅ Streaming server is running!');
    console.log('📋 Manual setup required:');
    console.log('  • Load the Chrome extension from: ./chrome-extension/');
    console.log('  • Open Facebook and Google tabs');
    console.log('  • Use the extension to start video capture');
    console.log('  • View streams at: http://localhost:8080');
    console.log('');
    
    await prompt('Press Enter to stop the server...');
    await this.cleanup();
  }

  async startStreamingServer() {
    console.log('🚀 Starting streaming server...');
    
    const serverProcess = spawn('node', ['streaming-server/server.js'], {
      cwd: __dirname,
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    this.processes.set('server', serverProcess);
    
    serverProcess.stdout.on('data', (data) => {
      console.log(`[SERVER] ${data.toString().trim()}`);
    });
    
    serverProcess.stderr.on('data', (data) => {
      console.error(`[SERVER ERROR] ${data.toString().trim()}`);
    });
    
    serverProcess.on('close', (code) => {
      console.log(`[SERVER] Process exited with code ${code}`);
      this.processes.delete('server');
    });
    
    // Wait for server to start
    await this.sleep(1000);
  }

  async startPuppeteerController(args = []) {
    console.log('🎭 Starting Puppeteer controller...');
    
    const puppeteerProcess = spawn('node', ['puppeteer-controller/demo.js', ...args], {
      cwd: __dirname,
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    this.processes.set('puppeteer', puppeteerProcess);
    
    puppeteerProcess.stdout.on('data', (data) => {
      console.log(`[PUPPETEER] ${data.toString().trim()}`);
    });
    
    puppeteerProcess.stderr.on('data', (data) => {
      console.error(`[PUPPETEER ERROR] ${data.toString().trim()}`);
    });
    
    puppeteerProcess.on('close', (code) => {
      console.log(`[PUPPETEER] Process exited with code ${code}`);
      this.processes.delete('puppeteer');
    });
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up...');
    
    for (const [name, process] of this.processes) {
      console.log(`Stopping ${name}...`);
      process.kill('SIGTERM');
      
      // Force kill after 5 seconds
      setTimeout(() => {
        if (!process.killed) {
          process.kill('SIGKILL');
        }
      }, 5000);
    }
    
    this.processes.clear();
    console.log('✅ Cleanup complete');
    rl.close();
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, shutting down...');
  if (global.demoOrchestrator) {
    await global.demoOrchestrator.cleanup();
  }
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, shutting down...');
  if (global.demoOrchestrator) {
    await global.demoOrchestrator.cleanup();
  }
  process.exit(0);
});

// Start the demo
const orchestrator = new DemoOrchestrator();
global.demoOrchestrator = orchestrator;
orchestrator.start().catch(error => {
  console.error('❌ Demo error:', error);
  process.exit(1);
});
