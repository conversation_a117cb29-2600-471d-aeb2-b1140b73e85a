import WebSocket, { WebSocketServer } from 'ws';
import http from 'http';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class StreamingServer {
  constructor(port = 8080) {
    this.port = port;
    this.server = null;
    this.wss = null;
    this.clients = new Map(); // Map of client connections
    this.activeStreams = new Map(); // Map of active video streams
    this.currentStreamTab = null;
  }

  async start() {
    console.log('🚀 Starting Video Streaming Server...');
    
    // Create HTTP server
    this.server = http.createServer((req, res) => {
      this.handleHttpRequest(req, res);
    });
    
    // Create WebSocket server
    this.wss = new WebSocketServer({ server: this.server });
    
    // Handle WebSocket connections
    this.wss.on('connection', (ws, req) => {
      this.handleWebSocketConnection(ws, req);
    });
    
    // Start listening
    this.server.listen(this.port, () => {
      console.log(`✅ Streaming server running on http://localhost:${this.port}`);
      console.log(`🔌 WebSocket endpoint: ws://localhost:${this.port}`);
    });
  }

  handleHttpRequest(req, res) {
    const url = req.url;
    
    // Serve static files for client UI
    if (url === '/' || url === '/index.html') {
      this.serveFile(res, '../client-ui/index.html', 'text/html');
    } else if (url === '/client.js') {
      this.serveFile(res, '../client-ui/client.js', 'application/javascript');
    } else if (url === '/style.css') {
      this.serveFile(res, '../client-ui/style.css', 'text/css');
    } else if (url === '/status') {
      // Server status endpoint
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        status: 'running',
        clients: this.clients.size,
        activeStreams: this.activeStreams.size,
        currentStreamTab: this.currentStreamTab,
        timestamp: new Date().toISOString()
      }));
    } else {
      // 404 Not Found
      res.writeHead(404, { 'Content-Type': 'text/plain' });
      res.end('Not Found');
    }
  }

  serveFile(res, filePath, contentType) {
    const fullPath = path.join(__dirname, filePath);
    
    fs.readFile(fullPath, (err, data) => {
      if (err) {
        res.writeHead(404, { 'Content-Type': 'text/plain' });
        res.end('File not found');
        return;
      }
      
      res.writeHead(200, { 'Content-Type': contentType });
      res.end(data);
    });
  }

  handleWebSocketConnection(ws, req) {
    const clientId = this.generateClientId();
    const clientType = this.getClientType(req);
    
    console.log(`🔌 New ${clientType} connection: ${clientId}`);
    
    // Store client connection
    this.clients.set(clientId, {
      ws: ws,
      type: clientType,
      connected: true,
      lastPing: Date.now()
    });
    
    // Send welcome message
    this.sendToClient(clientId, {
      type: 'welcome',
      clientId: clientId,
      clientType: clientType,
      serverStatus: {
        activeStreams: Array.from(this.activeStreams.keys()),
        currentStreamTab: this.currentStreamTab
      }
    });
    
    // Handle messages from client
    ws.on('message', (data) => {
      this.handleClientMessage(clientId, data);
    });
    
    // Handle client disconnect
    ws.on('close', () => {
      console.log(`❌ Client disconnected: ${clientId}`);
      this.clients.delete(clientId);
    });
    
    // Handle errors
    ws.on('error', (error) => {
      console.error(`❌ WebSocket error for ${clientId}:`, error);
      this.clients.delete(clientId);
    });
    
    // Set up ping/pong for connection health
    this.setupPingPong(clientId);
  }

  getClientType(req) {
    const userAgent = req.headers['user-agent'] || '';
    if (userAgent.includes('Chrome') && req.headers['origin']?.includes('chrome-extension://')) {
      return 'extension';
    }
    return 'viewer';
  }

  generateClientId() {
    return 'client_' + Math.random().toString(36).substr(2, 9);
  }

  handleClientMessage(clientId, data) {
    try {
      const message = JSON.parse(data.toString());
      const client = this.clients.get(clientId);
      
      if (!client) return;
      
      console.log(`📨 Message from ${clientId} (${client.type}):`, message.type);
      
      switch (message.type) {
        case 'videoData':
          this.handleVideoData(clientId, message);
          break;
          
        case 'switchStream':
          this.handleStreamSwitch(clientId, message);
          break;
          
        case 'startCapture':
          this.handleStartCapture(clientId, message);
          break;
          
        case 'stopCapture':
          this.handleStopCapture(clientId, message);
          break;
          
        case 'ping':
          this.sendToClient(clientId, { type: 'pong' });
          break;
          
        case 'getStatus':
          this.sendServerStatus(clientId);
          break;
          
        default:
          console.warn(`⚠️  Unknown message type: ${message.type}`);
      }
    } catch (error) {
      console.error(`❌ Error handling message from ${clientId}:`, error);
    }
  }

  handleVideoData(clientId, message) {
    const { tabId, data } = message;
    
    // Store stream data
    this.activeStreams.set(tabId, {
      data: data,
      timestamp: Date.now(),
      sourceClient: clientId
    });
    
    // Forward to all viewer clients
    this.broadcastToViewers({
      type: 'videoStream',
      tabId: tabId,
      data: data,
      timestamp: Date.now()
    });
  }

  handleStreamSwitch(clientId, message) {
    const { tabId } = message;
    this.currentStreamTab = tabId;
    
    console.log(`🔄 Stream switched to tab: ${tabId}`);
    
    // Notify all clients about stream switch
    this.broadcastToAll({
      type: 'streamSwitched',
      tabId: tabId,
      timestamp: Date.now()
    });
  }

  handleStartCapture(clientId, message) {
    const { tabId } = message;
    
    console.log(`▶️  Capture started for tab: ${tabId}`);
    
    this.broadcastToAll({
      type: 'captureStarted',
      tabId: tabId,
      timestamp: Date.now()
    });
  }

  handleStopCapture(clientId, message) {
    const { tabId } = message;
    
    console.log(`⏹️  Capture stopped for tab: ${tabId}`);
    
    // Remove stream data
    this.activeStreams.delete(tabId);
    
    if (this.currentStreamTab === tabId) {
      this.currentStreamTab = null;
    }
    
    this.broadcastToAll({
      type: 'captureStopped',
      tabId: tabId,
      timestamp: Date.now()
    });
  }

  sendToClient(clientId, message) {
    const client = this.clients.get(clientId);
    if (client && client.ws.readyState === WebSocket.OPEN) {
      client.ws.send(JSON.stringify(message));
    }
  }

  broadcastToAll(message) {
    this.clients.forEach((client, clientId) => {
      this.sendToClient(clientId, message);
    });
  }

  broadcastToViewers(message) {
    this.clients.forEach((client, clientId) => {
      if (client.type === 'viewer') {
        this.sendToClient(clientId, message);
      }
    });
  }

  broadcastToExtensions(message) {
    this.clients.forEach((client, clientId) => {
      if (client.type === 'extension') {
        this.sendToClient(clientId, message);
      }
    });
  }

  sendServerStatus(clientId) {
    this.sendToClient(clientId, {
      type: 'serverStatus',
      status: {
        clients: this.clients.size,
        activeStreams: Array.from(this.activeStreams.keys()),
        currentStreamTab: this.currentStreamTab,
        timestamp: Date.now()
      }
    });
  }

  setupPingPong(clientId) {
    const client = this.clients.get(clientId);
    if (!client) return;
    
    const pingInterval = setInterval(() => {
      if (!this.clients.has(clientId)) {
        clearInterval(pingInterval);
        return;
      }
      
      this.sendToClient(clientId, { type: 'ping' });
      client.lastPing = Date.now();
    }, 30000); // Ping every 30 seconds
  }

  stop() {
    console.log('🛑 Stopping streaming server...');
    
    if (this.wss) {
      this.wss.close();
    }
    
    if (this.server) {
      this.server.close();
    }
    
    console.log('✅ Streaming server stopped');
  }
}

// Create and start server
const server = new StreamingServer(8080);
server.start();

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  server.stop();
  process.exit(0);
});

export default StreamingServer;
