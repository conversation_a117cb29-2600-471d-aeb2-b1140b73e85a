# Chrome DevTools Protocol + Extension Video Streaming Demo

A comprehensive demonstration project that combines Chrome DevTools Protocol (CDP) with Chrome extensions to capture video streams from multiple browser tabs and stream them to an external client UI.

## 🎯 Project Overview

This project demonstrates how to:

- Use Puppeteer to programmatically control Chrome with CDP enabled
- Develop a Chrome extension that captures video streams from browser tabs
- Stream captured video to an external client UI via WebSocket
- Switch between capturing different tabs (Facebook and Google)
- Manage video streams with proper cleanup and error handling

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Puppeteer     │    │  Chrome Browser  │    │  Streaming      │
│   Controller    │───▶│  + Extension     │───▶│  Server         │
│                 │    │                  │    │  (WebSocket)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │  Facebook Tab    │    │  Client UI      │
                       │  Google Tab      │    │  (Web Browser)  │
                       └──────────────────┘    └─────────────────┘
```

### Components

1. **Chrome Extension** (`chrome-extension/`)

   - Manifest V3 extension with tabCapture permissions
   - Background script for video capture and WebSocket communication
   - Content script for tab monitoring and visual indicators
   - Popup UI for manual control

2. **Puppeteer Controller** (`puppeteer-controller/`)

   - Launches Chrome with CDP enabled and extension loaded
   - Opens Facebook and Google tabs automatically
   - Provides programmatic control over browser instances

3. **Streaming Server** (`streaming-server/`)

   - WebSocket server for real-time video streaming
   - Handles multiple client connections
   - Manages stream switching and cleanup

4. **Client UI** (`client-ui/`)
   - Web-based interface for viewing video streams
   - Controls for switching between tab streams
   - Real-time connection status and debug information

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ installed
- Chrome browser (latest version recommended)
- Git (for cloning)

### Installation

1. **Clone or navigate to the project directory:**

   ```bash
   cd video-stream-demo
   ```

2. **Install dependencies:**

   ```bash
   npm install
   ```

3. **Run the demo:**

   ```bash
   npm start
   ```

4. **Choose demo mode:**
   - **Interactive**: Manual control with guided setup
   - **Automated**: Scripted demonstration
   - **Server only**: Manual extension setup required

### Alternative Setup Methods

**Start individual components:**

```bash
# Terminal 1: Start streaming server
npm run start-server

# Terminal 2: Start Puppeteer controller
npm run start-puppeteer

# Or run automated demo
npm run start-puppeteer-auto
```

**Development mode (both components):**

```bash
npm run dev
```

## 📋 Usage Instructions

### 1. Automated Demo (Recommended for first-time users)

```bash
npm start
# Choose option 2 (Automated)
```

This will:

- Start the streaming server
- Launch Chrome with the extension
- Open Facebook and Google tabs
- Demonstrate automatic tab switching
- Keep browser open for manual testing

### 2. Interactive Demo

```bash
npm start
# Choose option 1 (Interactive)
```

Manual steps:

1. Wait for Chrome to open with Facebook and Google tabs
2. Open http://localhost:8080 in another browser window
3. Click the extension icon in Chrome
4. Use "Start Capture" on either tab
5. Use the client UI to switch between streams

### 3. Manual Setup

```bash
npm start
# Choose option 3 (Server only)
```

Manual steps:

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode"
3. Click "Load unpacked" and select the `chrome-extension` folder
4. Open Facebook and Google tabs
5. Use the extension popup to start video capture
6. View streams at http://localhost:8080

## 🔧 Configuration

### Chrome Extension Permissions

The extension requires these permissions:

- `tabCapture`: For capturing video from browser tabs
- `activeTab`: For accessing current tab information
- `tabs`: For managing multiple tabs
- `storage`: For storing extension settings

### WebSocket Server Settings

Default configuration in `streaming-server/server.js`:

```javascript
const port = 8080; // WebSocket server port
const frameRate = 30; // Video frame rate
const maxClients = 10; // Maximum concurrent clients
```

### Video Capture Settings

Default settings in `chrome-extension/background.js`:

```javascript
const videoConstraints = {
  maxWidth: 1920,
  maxHeight: 1080,
  maxFrameRate: 30,
};
```

## 🐛 Troubleshooting

### Common Issues

**1. Extension not loading:**

- Ensure Chrome Developer mode is enabled
- Check for manifest.json syntax errors
- Verify all extension files are present

**2. Video capture fails:**

- Grant screen capture permissions when prompted
- Ensure tabs are fully loaded before capturing
- Check Chrome version compatibility (Chrome 88+)

**3. WebSocket connection fails:**

- Verify port 8080 is not in use
- Check firewall settings
- Ensure server is running before starting capture

**4. No video in client UI:**

- Check browser console for errors
- Verify WebSocket connection status
- Ensure video codec support (WebM/VP8)

### Debug Mode

Enable debug mode in the client UI:

1. Open http://localhost:8080
2. Check "Show Debug Info"
3. Monitor WebSocket messages and stream statistics

### Logs and Monitoring

**Server logs:**

```bash
# Server console shows:
# - Client connections/disconnections
# - Video data transfer rates
# - Error messages
```

**Extension logs:**

```bash
# Chrome DevTools Console (Extension background page):
# - Capture start/stop events
# - WebSocket connection status
# - Tab switching events
```

## 📁 Project Structure

```
video-stream-demo/
├── chrome-extension/          # Chrome extension files
│   ├── manifest.json         # Extension manifest
│   ├── background.js         # Service worker
│   ├── content.js           # Content script
│   ├── popup.html           # Extension popup
│   ├── popup.js             # Popup logic
│   └── injected.js          # Page injection script
├── puppeteer-controller/      # Browser automation
│   ├── cdp-controller.js    # CDP management class
│   └── demo.js              # Demo orchestration
├── streaming-server/          # WebSocket server
│   └── server.js            # Server implementation
├── client-ui/                 # Web client interface
│   ├── index.html           # Client UI
│   ├── style.css            # Styling
│   └── client.js            # Client logic
├── demo.js                   # Main demo launcher
├── package.json              # Dependencies
└── README.md                 # This file
```

## 🔍 Technical Details

### Video Capture Flow

1. **Extension requests tab capture** using `chrome.tabCapture.capture()`
2. **MediaRecorder** encodes video stream as WebM chunks
3. **WebSocket** sends binary data to streaming server
4. **Server** forwards data to connected client UIs
5. **Client** reconstructs video stream for playback

### Stream Switching Mechanism

1. **Client UI** sends switch request to server
2. **Server** forwards request to extension
3. **Extension** stops current capture and starts new one
4. **New video data** flows through the same pipeline

### Error Handling

- Automatic reconnection for WebSocket connections
- Graceful fallback when tabs are closed
- Stream cleanup on extension unload
- Client-side error recovery

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

For issues and questions:

1. Check the troubleshooting section above
2. Review browser console logs
3. Verify all prerequisites are met
4. Test with a fresh Chrome profile

## 🎬 Demo Video and Screenshots

### Expected Behavior

When running the demo, you should see:

1. **Chrome Browser** opens with:

   - Facebook tab loaded
   - Google tab loaded
   - Extension icon in toolbar

2. **Client UI** (http://localhost:8080) shows:

   - Connection status indicator
   - Stream control buttons
   - Video player area
   - Debug information panel

3. **Video Streaming** demonstrates:
   - Real-time tab capture
   - Smooth switching between tabs
   - Live video playback in client UI

### Key Features Demonstrated

- **Tab Capture**: Extension captures video from browser tabs
- **Stream Switching**: Toggle between Facebook and Google streams
- **Real-time Communication**: WebSocket-based video streaming
- **CDP Integration**: Puppeteer controls browser programmatically
- **Error Handling**: Graceful recovery from connection issues

## 🔧 Advanced Configuration

### Custom Tab URLs

Modify `puppeteer-controller/cdp-controller.js` to open different websites:

```javascript
// Change these URLs in the openTargetTabs() method
await facebookPage.goto("https://your-custom-site1.com");
await googlePage.goto("https://your-custom-site2.com");
```

### Video Quality Settings

Adjust capture quality in `chrome-extension/background.js`:

```javascript
const videoConstraints = {
  mandatory: {
    chromeMediaSource: "tab",
    maxWidth: 1280, // Lower for better performance
    maxHeight: 720, // Lower for better performance
    maxFrameRate: 15, // Lower for better performance
  },
};
```

### Server Port Configuration

Change the WebSocket port in multiple files:

1. `streaming-server/server.js`: Update `port` variable
2. `chrome-extension/background.js`: Update WebSocket URL
3. `client-ui/client.js`: Update WebSocket URL

## 🔮 Future Enhancements

- Support for additional websites
- Audio-only streaming mode
- Recording and playback features
- Multi-tab simultaneous capture
- Stream quality controls
- Mobile device support
- Screen recording capabilities
- Multi-user collaboration features
