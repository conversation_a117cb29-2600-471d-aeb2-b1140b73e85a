// Popup script for video stream capture extension
let targetTabs = [];
let currentCaptureTabId = null;

document.addEventListener("DOMContentLoaded", async () => {
  await findTargetTabs();
  updateUI();
  checkServerConnection();
});

async function findTargetTabs() {
  try {
    const tabs = await chrome.tabs.query({});
    targetTabs = tabs.filter(
      (tab) => tab.url.includes("github.com") || tab.url.includes("google.com")
    );

    if (targetTabs.length === 0) {
      // If no target tabs found, show message
      document.getElementById("tabsList").innerHTML = `
        <div style="text-align: center; padding: 20px; color: #666;">
          <p>No github or Google tabs found.</p>
          <p>Please open github.com and google.com in separate tabs.</p>
        </div>
      `;
      return;
    }

    displayTabs();
  } catch (error) {
    console.error("Error finding tabs:", error);
  }
}

function displayTabs() {
  const tabsList = document.getElementById("tabsList");
  tabsList.innerHTML = "";

  targetTabs.forEach((tab) => {
    const tabDiv = document.createElement("div");
    const tabClass = tab.url.includes("github.com") ? "github" : "google";

    tabDiv.className = `tab-info ${tabClass}`;
    tabDiv.innerHTML = `
      <div class="tab-title">${tab.title}</div>
      <div class="tab-url">${tab.url}</div>
      <div class="controls">
        <button class="start-btn" onclick="startCapture(${tab.id})">
          Start Capture
        </button>
        <button class="switch-btn" onclick="switchToTab(${tab.id})" 
                ${currentCaptureTabId ? "" : "disabled"}>
          Switch To This
        </button>
        <button class="stop-btn" onclick="stopCapture(${tab.id})">
          Stop
        </button>
      </div>
    `;

    tabsList.appendChild(tabDiv);
  });
}

async function startCapture(tabId) {
  try {
    // Request permission for tab capture
    const stream = await navigator.mediaDevices.getDisplayMedia({
      video: { mediaSource: "tab" },
      audio: true,
    });

    // Send message to background script
    chrome.runtime.sendMessage({
      action: "startCapture",
      tabId: tabId,
    });

    currentCaptureTabId = tabId;
    updateCaptureStatus(`Capturing Tab ${tabId}`);
    updateUI();
  } catch (error) {
    console.error("Failed to start capture:", error);
    updateCaptureStatus("Failed to start capture");
  }
}

function stopCapture(tabId) {
  chrome.runtime.sendMessage({
    action: "stopCapture",
    tabId: tabId,
  });

  if (currentCaptureTabId === tabId) {
    currentCaptureTabId = null;
  }

  updateCaptureStatus("No active capture");
  updateUI();
}

function switchToTab(tabId) {
  chrome.runtime.sendMessage({
    action: "switchTab",
    tabId: tabId,
  });

  currentCaptureTabId = tabId;
  updateCaptureStatus(`Switched to Tab ${tabId}`);
  updateUI();
}

function updateCaptureStatus(message) {
  const statusDiv = document.getElementById("captureStatus");
  statusDiv.textContent = message;

  if (currentCaptureTabId) {
    statusDiv.className = "status active";
  } else {
    statusDiv.className = "status inactive";
  }
}

function updateUI() {
  // Update button states based on current capture
  const buttons = document.querySelectorAll("button");
  buttons.forEach((button) => {
    if (button.textContent === "Switch To This") {
      button.disabled = !currentCaptureTabId;
    }
  });
}

function checkServerConnection() {
  // Try to connect to the streaming server to check status
  const ws = new WebSocket("ws://localhost:8080");
  const serverStatus = document.getElementById("serverStatus");

  ws.onopen = () => {
    serverStatus.textContent = "Streaming Server: Connected";
    serverStatus.className = "server-status server-connected";
    ws.close();
  };

  ws.onerror = () => {
    serverStatus.textContent = "Streaming Server: Disconnected";
    serverStatus.className = "server-status server-disconnected";
  };

  ws.onclose = () => {
    if (serverStatus.className.includes("server-connected")) {
      return; // Already updated in onopen
    }
    serverStatus.textContent = "Streaming Server: Disconnected";
    serverStatus.className = "server-status server-disconnected";
  };
}

// Listen for messages from background script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === "captureStarted") {
    currentCaptureTabId = message.tabId;
    updateCaptureStatus(`Capturing Tab ${message.tabId}`);
    updateUI();
  }
});
