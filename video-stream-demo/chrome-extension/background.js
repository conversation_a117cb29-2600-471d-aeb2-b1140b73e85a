// Background script for video stream capture
let activeStreams = new Map();
let websocketConnection = null;
let currentCaptureTabId = null;

// WebSocket connection to streaming server
function connectToStreamingServer() {
  try {
    websocketConnection = new WebSocket("ws://localhost:8080");

    websocketConnection.onopen = () => {
      console.log("Connected to streaming server");
    };

    websocketConnection.onclose = () => {
      console.log("Disconnected from streaming server");
      // Attempt to reconnect after 3 seconds
      setTimeout(connectToStreamingServer, 3000);
    };

    websocketConnection.onerror = (error) => {
      console.error("WebSocket error:", error);
    };
  } catch (error) {
    console.error("Failed to connect to streaming server:", error);
  }
}

// Initialize connection on startup
connectToStreamingServer();

// Listen for messages from popup and content scripts
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch (message.action) {
    case "startCapture":
      startTabCapture(message.tabId);
      break;
    case "stopCapture":
      stopTabCapture(message.tabId);
      break;
    case "switchTab":
      switchCaptureTab(message.tabId);
      break;
    case "switchToTabType":
      switchToTabType(message.tabType);
      break;
    case "getActiveStreams":
      sendResponse({ streams: Array.from(activeStreams.keys()) });
      break;
    case "getCurrentCapture":
      sendResponse({
        currentTabId: currentCaptureTabId,
        activeStreams: Array.from(activeStreams.keys()),
      });
      break;
  }
});

async function startTabCapture(tabId) {
  try {
    // Stop any existing capture
    if (currentCaptureTabId && currentCaptureTabId !== tabId) {
      stopTabCapture(currentCaptureTabId);
    }

    // Check if already capturing this tab
    if (currentCaptureTabId === tabId && activeStreams.has(tabId)) {
      console.log(`Already capturing tab ${tabId}`);
      return;
    }

    // Get tab information
    const tab = await chrome.tabs.get(tabId);
    console.log(`Starting capture for tab: ${tab.title} (${tab.url})`);

    // Notify server about capture start
    if (
      websocketConnection &&
      websocketConnection.readyState === WebSocket.OPEN
    ) {
      websocketConnection.send(
        JSON.stringify({
          type: "startCapture",
          tabId: getTabType(tab.url),
          tabInfo: {
            title: tab.title,
            url: tab.url,
            id: tabId,
          },
        })
      );
    }

    const stream = await chrome.tabCapture.capture({
      audio: true,
      video: true,
      videoConstraints: {
        mandatory: {
          chromeMediaSource: "tab",
          maxWidth: 1920,
          maxHeight: 1080,
          maxFrameRate: 30,
        },
      },
    });

    if (stream) {
      activeStreams.set(tabId, stream);
      currentCaptureTabId = tabId;

      // Set up MediaRecorder to stream data
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: "video/webm;codecs=vp8,opus",
      });

      let frameCount = 0;
      let lastFrameTime = Date.now();

      mediaRecorder.ondataavailable = (event) => {
        if (
          event.data.size > 0 &&
          websocketConnection &&
          websocketConnection.readyState === WebSocket.OPEN
        ) {
          frameCount++;
          const currentTime = Date.now();

          // Convert blob to array buffer and send
          event.data.arrayBuffer().then((buffer) => {
            const message = {
              type: "videoData",
              tabId: getTabType(tab.url),
              data: Array.from(new Uint8Array(buffer)),
              metadata: {
                frameCount: frameCount,
                timestamp: currentTime,
                size: buffer.byteLength,
                tabInfo: {
                  title: tab.title,
                  url: tab.url,
                  id: tabId,
                },
              },
            };
            websocketConnection.send(JSON.stringify(message));
          });

          lastFrameTime = currentTime;
        }
      };

      mediaRecorder.onerror = (event) => {
        console.error("MediaRecorder error:", event.error);
        stopTabCapture(tabId);
      };

      mediaRecorder.start(100); // Send data every 100ms

      // Store recorder reference
      activeStreams.set(tabId + "_recorder", mediaRecorder);

      console.log(`Started capturing tab ${tabId} (${getTabType(tab.url)})`);

      // Notify popup and content script of successful capture
      chrome.runtime.sendMessage({
        action: "captureStarted",
        tabId: tabId,
        tabType: getTabType(tab.url),
      });

      // Notify content script in the captured tab
      chrome.tabs
        .sendMessage(tabId, {
          action: "captureStarted",
        })
        .catch(() => {
          // Ignore errors if content script not loaded
        });
    }
  } catch (error) {
    console.error("Failed to start tab capture:", error);

    // Notify server about capture failure
    if (
      websocketConnection &&
      websocketConnection.readyState === WebSocket.OPEN
    ) {
      websocketConnection.send(
        JSON.stringify({
          type: "captureError",
          tabId: tabId,
          error: error.message,
        })
      );
    }
  }
}

function stopTabCapture(tabId) {
  const stream = activeStreams.get(tabId);
  const recorder = activeStreams.get(tabId + "_recorder");

  if (recorder) {
    recorder.stop();
    activeStreams.delete(tabId + "_recorder");
  }

  if (stream) {
    stream.getTracks().forEach((track) => track.stop());
    activeStreams.delete(tabId);
  }

  if (currentCaptureTabId === tabId) {
    currentCaptureTabId = null;
  }

  // Notify server about capture stop
  chrome.tabs
    .get(tabId)
    .then((tab) => {
      if (
        websocketConnection &&
        websocketConnection.readyState === WebSocket.OPEN
      ) {
        websocketConnection.send(
          JSON.stringify({
            type: "stopCapture",
            tabId: getTabType(tab.url),
            tabInfo: {
              title: tab.title,
              url: tab.url,
              id: tabId,
            },
          })
        );
      }
    })
    .catch(() => {
      // Tab might be closed, send generic stop message
      if (
        websocketConnection &&
        websocketConnection.readyState === WebSocket.OPEN
      ) {
        websocketConnection.send(
          JSON.stringify({
            type: "stopCapture",
            tabId: tabId,
          })
        );
      }
    });

  // Notify content script in the tab
  chrome.tabs
    .sendMessage(tabId, {
      action: "captureStopped",
    })
    .catch(() => {
      // Ignore errors if content script not loaded or tab closed
    });

  console.log(`Stopped capturing tab ${tabId}`);
}

async function switchCaptureTab(newTabId) {
  console.log(`Switching capture to tab ${newTabId}`);

  try {
    const newTab = await chrome.tabs.get(newTabId);
    const newTabType = getTabType(newTab.url);

    // Notify server about switch
    if (
      websocketConnection &&
      websocketConnection.readyState === WebSocket.OPEN
    ) {
      websocketConnection.send(
        JSON.stringify({
          type: "switchStream",
          tabId: newTabType,
          tabInfo: {
            title: newTab.title,
            url: newTab.url,
            id: newTabId,
          },
        })
      );
    }

    // Stop current capture and start new one
    if (currentCaptureTabId && currentCaptureTabId !== newTabId) {
      stopTabCapture(currentCaptureTabId);
    }

    await startTabCapture(newTabId);
  } catch (error) {
    console.error("Failed to switch capture tab:", error);
  }
}

// Helper function to determine tab type from URL
function getTabType(url) {
  if (url.includes("github.com")) {
    return "github";
  } else if (url.includes("google.com")) {
    return "google";
  } else {
    return "unknown";
  }
}

// Helper function to find tabs by type
async function findTabsByType(type) {
  const tabs = await chrome.tabs.query({});
  return tabs.filter((tab) => getTabType(tab.url) === type);
}

async function switchToTabType(tabType) {
  try {
    const tabs = await findTabsByType(tabType);
    if (tabs.length > 0) {
      await switchCaptureTab(tabs[0].id);
    } else {
      console.warn(`No ${tabType} tabs found`);
    }
  } catch (error) {
    console.error(`Failed to switch to ${tabType} tab:`, error);
  }
}

// Clean up on extension unload
chrome.runtime.onSuspend.addListener(() => {
  activeStreams.forEach((stream, tabId) => {
    if (typeof tabId === "number") {
      stopTabCapture(tabId);
    }
  });

  if (websocketConnection) {
    websocketConnection.close();
  }
});
