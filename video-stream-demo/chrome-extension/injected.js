// Injected script to access page's MediaStream APIs
(function() {
  'use strict';
  
  // Store original MediaStream methods
  const originalGetUserMedia = navigator.mediaDevices.getUserMedia.bind(navigator.mediaDevices);
  const originalGetDisplayMedia = navigator.mediaDevices.getDisplayMedia.bind(navigator.mediaDevices);
  
  // Override getUserMedia to intercept media streams
  navigator.mediaDevices.getUserMedia = function(constraints) {
    console.log('getUserMedia called with constraints:', constraints);
    
    return originalGetUserMedia(constraints).then(stream => {
      // Notify extension about new stream
      window.postMessage({
        type: 'STREAM_CAPTURE_REQUEST',
        payload: {
          type: 'userMedia',
          constraints: constraints,
          streamId: stream.id
        }
      }, '*');
      
      return stream;
    });
  };
  
  // Override getDisplayMedia to intercept screen capture
  navigator.mediaDevices.getDisplayMedia = function(constraints) {
    console.log('getDisplayMedia called with constraints:', constraints);
    
    return originalGetDisplayMedia(constraints).then(stream => {
      // Notify extension about new display stream
      window.postMessage({
        type: 'STREAM_CAPTURE_REQUEST',
        payload: {
          type: 'displayMedia',
          constraints: constraints,
          streamId: stream.id
        }
      }, '*');
      
      return stream;
    });
  };
  
  // Listen for messages from content script
  window.addEventListener('message', function(event) {
    if (event.source !== window) return;
    
    if (event.data.type === 'STREAM_CAPTURE_INJECT') {
      console.log('Stream capture injection received:', event.data.payload);
      // Handle any specific injection logic here
    }
  });
  
  // Detect video elements and notify extension
  function detectVideoElements() {
    const videos = document.querySelectorAll('video');
    if (videos.length > 0) {
      window.postMessage({
        type: 'STREAM_CAPTURE_REQUEST',
        payload: {
          type: 'videoElements',
          count: videos.length,
          videos: Array.from(videos).map(video => ({
            src: video.src,
            currentSrc: video.currentSrc,
            duration: video.duration,
            paused: video.paused
          }))
        }
      }, '*');
    }
  }
  
  // Monitor for new video elements
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach(function(node) {
          if (node.nodeType === Node.ELEMENT_NODE) {
            if (node.tagName === 'VIDEO' || node.querySelector('video')) {
              detectVideoElements();
            }
          }
        });
      }
    });
  });
  
  // Start observing
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
  // Initial detection
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', detectVideoElements);
  } else {
    detectVideoElements();
  }
  
})();
