{"manifest_version": 3, "name": "Multi-Tab Video Stream Capture", "version": "1.0.0", "description": "Capture video streams from multiple browser tabs and stream to external client", "permissions": ["tabCapture", "activeTab", "tabs", "storage", "scripting"], "host_permissions": ["https://facebook.com/*", "https://www.facebook.com/*", "https://google.com/*", "https://www.google.com/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"]}], "action": {"default_popup": "popup.html", "default_title": "Video Stream Capture"}, "web_accessible_resources": [{"resources": ["injected.js"], "matches": ["<all_urls>"]}]}