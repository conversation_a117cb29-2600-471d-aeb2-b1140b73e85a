// Content script for video stream capture
(function() {
  'use strict';
  
  // Inject script to access page's MediaStream APIs
  const script = document.createElement('script');
  script.src = chrome.runtime.getURL('injected.js');
  script.onload = function() {
    this.remove();
  };
  (document.head || document.documentElement).appendChild(script);
  
  // Listen for messages from injected script
  window.addEventListener('message', function(event) {
    if (event.source !== window) return;
    
    if (event.data.type === 'STREAM_CAPTURE_REQUEST') {
      // Forward to background script
      chrome.runtime.sendMessage({
        action: 'streamCaptureRequest',
        data: event.data.payload
      });
    }
  });
  
  // Listen for messages from background script
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === 'injectStreamCapture') {
      // Notify injected script
      window.postMessage({
        type: 'STREAM_CAPTURE_INJECT',
        payload: message.data
      }, '*');
    }
  });
  
  // Add visual indicator when tab is being captured
  let captureIndicator = null;
  
  function showCaptureIndicator() {
    if (captureIndicator) return;
    
    captureIndicator = document.createElement('div');
    captureIndicator.id = 'stream-capture-indicator';
    captureIndicator.innerHTML = `
      <div style="
        position: fixed;
        top: 10px;
        right: 10px;
        background: #ff4444;
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-family: Arial, sans-serif;
        font-size: 12px;
        font-weight: bold;
        z-index: 999999;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        animation: pulse 2s infinite;
      ">
        🔴 RECORDING
      </div>
      <style>
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.7; }
          100% { opacity: 1; }
        }
      </style>
    `;
    
    document.body.appendChild(captureIndicator);
  }
  
  function hideCaptureIndicator() {
    if (captureIndicator) {
      captureIndicator.remove();
      captureIndicator = null;
    }
  }
  
  // Listen for capture status changes
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === 'captureStarted') {
      showCaptureIndicator();
    } else if (message.action === 'captureStopped') {
      hideCaptureIndicator();
    }
  });
  
  // Check if this tab is currently being captured
  chrome.runtime.sendMessage({ action: 'isTabBeingCaptured' }, (response) => {
    if (response && response.isCapturing) {
      showCaptureIndicator();
    }
  });
  
})();
