<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 350px;
      padding: 20px;
      font-family: Arial, sans-serif;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .tab-info {
      background: #f5f5f5;
      padding: 10px;
      margin: 10px 0;
      border-radius: 5px;
      border-left: 4px solid #4285f4;
    }
    
    .tab-info.facebook {
      border-left-color: #1877f2;
    }
    
    .tab-info.google {
      border-left-color: #4285f4;
    }
    
    .tab-title {
      font-weight: bold;
      margin-bottom: 5px;
    }
    
    .tab-url {
      font-size: 12px;
      color: #666;
      margin-bottom: 10px;
    }
    
    .controls {
      display: flex;
      gap: 10px;
    }
    
    button {
      flex: 1;
      padding: 8px 12px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
    }
    
    .start-btn {
      background: #4CAF50;
      color: white;
    }
    
    .stop-btn {
      background: #f44336;
      color: white;
    }
    
    .switch-btn {
      background: #2196F3;
      color: white;
    }
    
    button:hover {
      opacity: 0.8;
    }
    
    button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    .status {
      text-align: center;
      margin: 15px 0;
      padding: 10px;
      border-radius: 4px;
    }
    
    .status.active {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status.inactive {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .server-status {
      font-size: 12px;
      text-align: center;
      margin-top: 15px;
      padding: 5px;
      border-radius: 3px;
    }
    
    .server-connected {
      background: #d1ecf1;
      color: #0c5460;
    }
    
    .server-disconnected {
      background: #f8d7da;
      color: #721c24;
    }
  </style>
</head>
<body>
  <div class="header">
    <h3>Multi-Tab Video Capture</h3>
  </div>
  
  <div class="status inactive" id="captureStatus">
    No active capture
  </div>
  
  <div id="tabsList">
    <!-- Tabs will be populated by JavaScript -->
  </div>
  
  <div class="server-status server-disconnected" id="serverStatus">
    Streaming Server: Disconnected
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
