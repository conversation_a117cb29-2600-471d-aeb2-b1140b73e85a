# Architecture Documentation

## System Overview

The Video Stream Demo is a multi-component system that demonstrates real-time video streaming from browser tabs using Chrome DevTools Protocol (CDP) and Chrome Extensions.

## Component Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        Demo Orchestrator                        │
│                         (demo.js)                              │
└─────────────────────────────────────────────────────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
                ▼               ▼               ▼
    ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
    │   Puppeteer     │ │  Streaming      │ │  Client UI      │
    │   Controller    │ │  Server         │ │  Application    │
    └─────────────────┘ └─────────────────┘ └─────────────────┘
                │               │               │
                ▼               ▼               ▼
    ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
    │  Chrome Browser │ │  WebSocket      │ │  Web Browser    │
    │  + Extension    │ │  Communication  │ │  (Viewer)       │
    └─────────────────┘ └─────────────────┘ └─────────────────┘
                │               │               │
                └───────────────┼───────────────┘
                                │
                        ┌─────────────────┐
                        │  Video Stream   │
                        │  Data Flow      │
                        └─────────────────┘
```

## Data Flow

### 1. Video Capture Flow

```
Tab Content → Chrome Extension → MediaRecorder → WebSocket → Server → Client UI
```

**Detailed Steps:**

1. **Tab Rendering**: <PERSON>rowser renders Facebook/Google content
2. **Capture Request**: Extension calls `chrome.tabCapture.capture()`
3. **Stream Creation**: Chrome creates MediaStream from tab
4. **Encoding**: MediaRecorder encodes stream as WebM chunks
5. **Transmission**: WebSocket sends binary data to server
6. **Distribution**: Server forwards data to connected clients
7. **Playback**: Client reconstructs and plays video stream

### 2. Control Flow

```
Client UI → WebSocket → Server → Extension → Chrome API
```

**Detailed Steps:**

1. **User Action**: User clicks "Switch to Github" in client UI
2. **Message Send**: Client sends switch request via WebSocket
3. **Server Relay**: Server forwards request to extension
4. **Tab Switch**: Extension stops current capture, starts new one
5. **Confirmation**: Success/failure propagated back to client

## Component Details

### Chrome Extension

**Files:**

- `manifest.json`: Extension configuration and permissions
- `background.js`: Service worker for video capture and communication
- `content.js`: Injected into tabs for monitoring and UI
- `popup.html/js`: Extension popup interface
- `injected.js`: Page-level script injection

**Key Responsibilities:**

- Video capture using `chrome.tabCapture` API
- WebSocket communication with streaming server
- Tab management and switching
- User interface for manual control

**APIs Used:**

- `chrome.tabCapture`: Video/audio capture from tabs
- `chrome.tabs`: Tab management and information
- `chrome.runtime`: Extension messaging
- `MediaRecorder`: Video encoding
- `WebSocket`: Real-time communication

### Puppeteer Controller

**Files:**

- `cdp-controller.js`: Main controller class
- `demo.js`: Demo orchestration and user interaction

**Key Responsibilities:**

- Launch Chrome with CDP enabled
- Load extension automatically
- Open target tabs (Facebook, Google)
- Provide programmatic browser control
- Monitor browser state and events

**Features:**

- Automatic extension loading
- Tab management
- Network monitoring
- Script injection
- Interactive and automated modes

### Streaming Server

**File:** `server.js`

**Key Responsibilities:**

- WebSocket server for real-time communication
- Client connection management
- Video data relay between extension and clients
- HTTP server for client UI
- Stream switching coordination

**Architecture:**

```javascript
class StreamingServer {
  - clients: Map<clientId, clientInfo>
  - activeStreams: Map<tabId, streamData>
  - currentStreamTab: string

  + handleWebSocketConnection()
  + handleVideoData()
  + broadcastToClients()
  + manageStreamSwitching()
}
```

### Client UI

**Files:**

- `index.html`: User interface structure
- `style.css`: Responsive styling
- `client.js`: WebSocket client and video handling

**Key Responsibilities:**

- Display video streams in real-time
- Provide stream switching controls
- Show connection status and debug info
- Handle WebSocket communication
- Manage video playback and quality

**Features:**

- Real-time video playback
- Stream switching controls
- Connection status monitoring
- Debug information panel
- Responsive design

## Communication Protocols

### WebSocket Message Format

```javascript
// Video Data Message
{
  type: 'videoData',
  tabId: 'facebook' | 'google',
  data: Uint8Array, // Video chunk as array
  metadata: {
    frameCount: number,
    timestamp: number,
    size: number,
    tabInfo: { title, url, id }
  }
}

// Control Messages
{
  type: 'switchStream',
  tabId: 'facebook' | 'google',
  tabInfo: { title, url, id }
}

{
  type: 'startCapture' | 'stopCapture',
  tabId: string,
  tabInfo: { title, url, id }
}

// Status Messages
{
  type: 'welcome' | 'serverStatus',
  clientId: string,
  status: { clients, activeStreams, currentStreamTab }
}
```

### Chrome Extension Messaging

```javascript
// Runtime Messages
chrome.runtime.sendMessage({
  action: "startCapture" | "stopCapture" | "switchTab",
  tabId: number,
  tabType: "facebook" | "google",
});

// Tab Messages
chrome.tabs.sendMessage(tabId, {
  action: "captureStarted" | "captureStopped",
});
```

## Security Model

### Extension Permissions

```json
{
  "permissions": [
    "tabCapture", // Video/audio capture
    "activeTab", // Current tab access
    "tabs", // Tab management
    "storage" // Settings storage
  ],
  "host_permissions": [
    "https://facebook.com/*",
    "https://www.facebook.com/*",
    "https://google.com/*",
    "https://www.google.com/*"
  ]
}
```

### Network Security

- **WebSocket**: Unencrypted (ws://) for demo purposes
- **CORS**: Disabled for development
- **Authentication**: None (demo only)

**Production Considerations:**

- Use WSS (WebSocket Secure) for encryption
- Implement authentication and authorization
- Add rate limiting and input validation
- Enable CORS with specific origins

## Performance Characteristics

### Video Streaming

- **Resolution**: Up to 1920x1080 (configurable)
- **Frame Rate**: Up to 30 FPS (configurable)
- **Codec**: WebM with VP8 video and Opus audio
- **Latency**: ~100-500ms depending on network
- **Bandwidth**: ~1-5 Mbps per stream

### Resource Usage

- **CPU**: Moderate (video encoding/decoding)
- **Memory**: ~50-200MB per active stream
- **Network**: Continuous upload/download during streaming
- **Storage**: Minimal (no recording by default)

## Scalability Considerations

### Current Limitations

- Single server instance
- No load balancing
- Limited concurrent connections
- No stream persistence

### Scaling Strategies

1. **Horizontal Scaling**:

   - Multiple server instances
   - Load balancer for WebSocket connections
   - Shared state management (Redis)

2. **Vertical Scaling**:

   - Increase server resources
   - Optimize video encoding
   - Implement connection pooling

3. **CDN Integration**:
   - Stream distribution via CDN
   - Edge server deployment
   - Reduced latency for global users

## Error Handling

### Extension Level

- MediaRecorder error recovery
- WebSocket reconnection logic
- Tab closure detection
- Permission denial handling

### Server Level

- Client disconnection handling
- Invalid message filtering
- Resource cleanup on errors
- Graceful shutdown procedures

### Client Level

- Video playback error recovery
- WebSocket reconnection
- Stream switching failures
- Network interruption handling

## Monitoring and Debugging

### Logging Points

1. **Extension**: Chrome DevTools console
2. **Server**: Node.js console output
3. **Client**: Browser DevTools console
4. **Puppeteer**: Process stdout/stderr

### Debug Features

- Real-time message logging
- Stream statistics tracking
- Connection status monitoring
- Performance metrics collection

### Troubleshooting Tools

- Chrome extension DevTools
- WebSocket connection inspector
- Network traffic analysis
- Video codec debugging
