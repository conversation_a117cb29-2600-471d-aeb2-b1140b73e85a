#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class SetupTester {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.passed = 0;
    this.total = 0;
  }

  test(description, testFn) {
    this.total++;
    try {
      const result = testFn();
      if (result) {
        console.log(`✅ ${description}`);
        this.passed++;
      } else {
        console.log(`❌ ${description}`);
        this.errors.push(description);
      }
    } catch (error) {
      console.log(`❌ ${description}: ${error.message}`);
      this.errors.push(`${description}: ${error.message}`);
    }
  }

  warn(description, testFn) {
    try {
      const result = testFn();
      if (result) {
        console.log(`✅ ${description}`);
      } else {
        console.log(`⚠️  ${description}`);
        this.warnings.push(description);
      }
    } catch (error) {
      console.log(`⚠️  ${description}: ${error.message}`);
      this.warnings.push(`${description}: ${error.message}`);
    }
  }

  fileExists(filePath) {
    return fs.existsSync(path.join(__dirname, filePath));
  }

  fileHasContent(filePath, minSize = 100) {
    const fullPath = path.join(__dirname, filePath);
    if (!fs.existsSync(fullPath)) return false;
    const stats = fs.statSync(fullPath);
    return stats.size > minSize;
  }

  jsonValid(filePath) {
    try {
      const content = fs.readFileSync(path.join(__dirname, filePath), 'utf8');
      JSON.parse(content);
      return true;
    } catch {
      return false;
    }
  }

  hasRequiredFields(filePath, fields) {
    try {
      const content = fs.readFileSync(path.join(__dirname, filePath), 'utf8');
      const json = JSON.parse(content);
      return fields.every(field => json.hasOwnProperty(field));
    } catch {
      return false;
    }
  }

  runTests() {
    console.log('🧪 Testing Video Stream Demo Setup');
    console.log('==================================\n');

    // Test project structure
    console.log('📁 Project Structure:');
    this.test('Root directory exists', () => this.fileExists('.'));
    this.test('Chrome extension directory exists', () => this.fileExists('chrome-extension'));
    this.test('Puppeteer controller directory exists', () => this.fileExists('puppeteer-controller'));
    this.test('Streaming server directory exists', () => this.fileExists('streaming-server'));
    this.test('Client UI directory exists', () => this.fileExists('client-ui'));

    console.log('\n📦 Package Configuration:');
    this.test('package.json exists', () => this.fileExists('package.json'));
    this.test('package.json is valid JSON', () => this.jsonValid('package.json'));
    this.test('package.json has required fields', () => 
      this.hasRequiredFields('package.json', ['name', 'version', 'scripts', 'dependencies']));

    console.log('\n🔧 Chrome Extension:');
    this.test('Extension manifest exists', () => this.fileExists('chrome-extension/manifest.json'));
    this.test('Extension manifest is valid JSON', () => this.jsonValid('chrome-extension/manifest.json'));
    this.test('Extension manifest has required fields', () =>
      this.hasRequiredFields('chrome-extension/manifest.json', ['manifest_version', 'name', 'permissions']));
    this.test('Background script exists', () => this.fileExists('chrome-extension/background.js'));
    this.test('Content script exists', () => this.fileExists('chrome-extension/content.js'));
    this.test('Popup HTML exists', () => this.fileExists('chrome-extension/popup.html'));
    this.test('Popup JS exists', () => this.fileExists('chrome-extension/popup.js'));
    this.test('Injected script exists', () => this.fileExists('chrome-extension/injected.js'));

    console.log('\n🎭 Puppeteer Controller:');
    this.test('CDP controller exists', () => this.fileExists('puppeteer-controller/cdp-controller.js'));
    this.test('Demo script exists', () => this.fileExists('puppeteer-controller/demo.js'));
    this.test('CDP controller has content', () => this.fileHasContent('puppeteer-controller/cdp-controller.js', 1000));
    this.test('Demo script has content', () => this.fileHasContent('puppeteer-controller/demo.js', 1000));

    console.log('\n🌐 Streaming Server:');
    this.test('Server script exists', () => this.fileExists('streaming-server/server.js'));
    this.test('Server script has content', () => this.fileHasContent('streaming-server/server.js', 1000));

    console.log('\n💻 Client UI:');
    this.test('Client HTML exists', () => this.fileExists('client-ui/index.html'));
    this.test('Client CSS exists', () => this.fileExists('client-ui/style.css'));
    this.test('Client JS exists', () => this.fileExists('client-ui/client.js'));
    this.test('Client HTML has content', () => this.fileHasContent('client-ui/index.html', 500));
    this.test('Client CSS has content', () => this.fileHasContent('client-ui/style.css', 1000));
    this.test('Client JS has content', () => this.fileHasContent('client-ui/client.js', 1000));

    console.log('\n📚 Documentation:');
    this.test('README exists', () => this.fileExists('README.md'));
    this.test('Setup guide exists', () => this.fileExists('SETUP.md'));
    this.test('Architecture doc exists', () => this.fileExists('ARCHITECTURE.md'));
    this.test('README has content', () => this.fileHasContent('README.md', 1000));

    console.log('\n🚀 Demo Scripts:');
    this.test('Main demo script exists', () => this.fileExists('demo.js'));
    this.test('Test setup script exists', () => this.fileExists('test-setup.js'));
    this.test('Demo script has content', () => this.fileHasContent('demo.js', 1000));

    // Check for Node.js dependencies
    console.log('\n📦 Dependencies:');
    this.warn('node_modules exists', () => this.fileExists('node_modules'));
    this.warn('Puppeteer installed', () => this.fileExists('node_modules/puppeteer'));
    this.warn('WebSocket library installed', () => this.fileExists('node_modules/ws'));

    // Check extension permissions
    console.log('\n🔐 Extension Permissions:');
    try {
      const manifest = JSON.parse(fs.readFileSync(path.join(__dirname, 'chrome-extension/manifest.json'), 'utf8'));
      this.test('Has tabCapture permission', () => manifest.permissions?.includes('tabCapture'));
      this.test('Has activeTab permission', () => manifest.permissions?.includes('activeTab'));
      this.test('Has tabs permission', () => manifest.permissions?.includes('tabs'));
      this.test('Has host permissions', () => manifest.host_permissions?.length > 0);
    } catch (error) {
      this.errors.push('Could not validate extension permissions');
    }

    // Summary
    console.log('\n📊 Test Summary:');
    console.log('================');
    console.log(`✅ Passed: ${this.passed}/${this.total}`);
    console.log(`❌ Failed: ${this.errors.length}`);
    console.log(`⚠️  Warnings: ${this.warnings.length}`);

    if (this.errors.length > 0) {
      console.log('\n❌ Errors:');
      this.errors.forEach(error => console.log(`  - ${error}`));
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  Warnings:');
      this.warnings.forEach(warning => console.log(`  - ${warning}`));
    }

    console.log('\n🎯 Next Steps:');
    if (this.errors.length === 0) {
      console.log('✅ Setup looks good! You can run:');
      console.log('   npm install  # Install dependencies');
      console.log('   npm start    # Start the demo');
    } else {
      console.log('❌ Please fix the errors above before running the demo.');
    }

    if (this.warnings.length > 0) {
      console.log('⚠️  Consider addressing warnings for the best experience.');
    }

    return this.errors.length === 0;
  }
}

// Run the tests
const tester = new SetupTester();
const success = tester.runTests();
process.exit(success ? 0 : 1);
