# Setup Instructions

## Quick Setup (Recommended)

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Run the demo:**
   ```bash
   npm start
   ```

3. **Choose "Automated" mode** for the best first experience

## Manual Setup

### Step 1: Install Dependencies

```bash
cd video-stream-demo
npm install
```

### Step 2: Load Chrome Extension

1. Open Chrome browser
2. Navigate to `chrome://extensions/`
3. Enable "Developer mode" (toggle in top right)
4. Click "Load unpacked"
5. Select the `chrome-extension` folder from this project
6. Note the extension ID that appears

### Step 3: Start Streaming Server

```bash
npm run start-server
```

The server will start on http://localhost:8080

### Step 4: Open Target Tabs

Either use Puppeteer (automated):
```bash
npm run start-puppeteer
```

Or manually:
1. Open https://www.facebook.com in one tab
2. Open https://www.google.com in another tab

### Step 5: Start Video Capture

1. Click the extension icon in Chrome toolbar
2. Click "Start Capture" for either Facebook or Google tab
3. <PERSON> screen capture permissions when prompted

### Step 6: View Stream

1. Open http://localhost:8080 in a separate browser window
2. Use the controls to switch between tab streams
3. Monitor connection status and debug info

## Verification Steps

### ✅ Extension Loaded Correctly
- Extension appears in Chrome toolbar
- No errors in `chrome://extensions/`
- Popup opens when clicking extension icon

### ✅ Server Running
- Console shows "Streaming server running on http://localhost:8080"
- http://localhost:8080 loads the client UI
- WebSocket connection shows "Connected" status

### ✅ Video Capture Working
- Extension popup shows available tabs
- "Start Capture" button works without errors
- Red recording indicator appears on captured tab

### ✅ Stream Switching
- Client UI shows both Facebook and Google buttons
- Clicking buttons switches the video stream
- Video player updates with new content

## Troubleshooting Common Issues

### Extension Won't Load
```bash
# Check manifest.json syntax
cat chrome-extension/manifest.json | jq .

# Verify all files exist
ls -la chrome-extension/
```

### Server Won't Start
```bash
# Check if port 8080 is in use
lsof -i :8080

# Try different port
PORT=8081 npm run start-server
```

### Video Capture Fails
1. Ensure Chrome version 88+
2. Grant screen capture permissions
3. Try refreshing the target tabs
4. Check Chrome DevTools console for errors

### No Video in Client
1. Check WebSocket connection status
2. Verify extension is capturing (red indicator)
3. Look for codec errors in browser console
4. Try different browser for client UI

## Development Setup

### Running in Development Mode

```bash
# Start both server and Puppeteer
npm run dev
```

### Debugging the Extension

1. Go to `chrome://extensions/`
2. Click "Inspect views: background page" under the extension
3. Use Chrome DevTools to debug background script

### Debugging the Server

```bash
# Run with debug output
DEBUG=* npm run start-server
```

### Debugging the Client

1. Open http://localhost:8080
2. Open browser DevTools (F12)
3. Check Console and Network tabs
4. Enable "Show Debug Info" in the client UI

## Performance Optimization

### Reduce Video Quality
Edit `chrome-extension/background.js`:
```javascript
videoConstraints: {
  mandatory: {
    maxWidth: 1280,    // Instead of 1920
    maxHeight: 720,    // Instead of 1080
    maxFrameRate: 15   // Instead of 30
  }
}
```

### Increase Streaming Interval
Edit `chrome-extension/background.js`:
```javascript
mediaRecorder.start(200); // Instead of 100ms
```

### Limit Concurrent Connections
Edit `streaming-server/server.js`:
```javascript
const maxClients = 5; // Add connection limit
```

## Security Considerations

### Development Only
This demo is for development/testing purposes only. For production use:

1. Add authentication to WebSocket server
2. Implement HTTPS/WSS encryption
3. Validate and sanitize all inputs
4. Add rate limiting
5. Implement proper error handling

### Chrome Extension Security
- Extension has broad permissions for demo purposes
- In production, use minimal required permissions
- Implement content security policy
- Validate all external communications

## Next Steps

After successful setup:

1. **Experiment** with different websites
2. **Modify** video quality settings
3. **Extend** the client UI with new features
4. **Add** support for additional browsers
5. **Implement** recording capabilities

## Getting Help

If you encounter issues:

1. Check this troubleshooting guide
2. Review the main README.md
3. Check browser console logs
4. Verify all prerequisites are met
5. Try with a fresh Chrome profile
